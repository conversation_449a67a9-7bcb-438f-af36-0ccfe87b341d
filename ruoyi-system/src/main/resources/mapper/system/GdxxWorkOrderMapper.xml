<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.GdxxWorkOrderMapper">

    <resultMap type="GdxxWorkOrder" id="GdxxWorkOrderResult">
        <result property="id" column="id"/>
        <result property="workOrderTitle" column="work_order_title"/>
        <result property="workOrderType" column="work_order_type"/>
        <result property="requesterId" column="requester_id"/>
        <result property="requesterDepartmentId" column="requester_department_id"/>
        <result property="requirementBackground" column="requirement_background"/>
        <result property="requirementPurpose" column="requirement_purpose"/>
        <result property="requirementDescription" column="requirement_description"/>
        <result property="requirementSubmissionTime" column="requirement_submission_time"/>
        <result property="acceptanceTime" column="acceptance_time"/>
        <result property="requirementPriority" column="requirement_priority"/>
        <result property="expectedCompletionDate" column="expected_completion_date"/>
        <result property="requirementRemark" column="requirement_remark"/>
        <result property="workOrderStatus" column="work_order_status"/>
        <result property="rndProgress" column="rnd_progress"/>
        <result property="requirementImplementationSystem" column="requirement_implementation_system"/>
        <result property="systemFunctionModule" column="system_function_module"/>
        <result property="projectRisk" column="project_risk"/>
        <result property="externalStakeholderInfo" column="external_stakeholder_info"/>
        <result property="currentExecutor" column="current_executor"/>
        <result property="requirementScheduleStartDate" column="requirement_schedule_start_date"/>
        <result property="requirementScheduleEndDate" column="requirement_schedule_end_date"/>
        <result property="designScheduleStartDate" column="design_schedule_start_date"/>
        <result property="designScheduleEndDate" column="design_schedule_end_date"/>
        <result property="developmentScheduleStartDate" column="development_schedule_start_date"/>
        <result property="developmentScheduleEndDate" column="development_schedule_end_date"/>
        <result property="testingScheduleStartDate" column="testing_schedule_start_date"/>
        <result property="testingScheduleEndDate" column="testing_schedule_end_date"/>
        <result property="acceptanceTestingScheduleStartDate" column="acceptance_testing_schedule_start_date"/>
        <result property="acceptanceTestingScheduleEndDate" column="acceptance_testing_schedule_end_date"/>
        <result property="expectedGoLiveDate" column="expected_go_live_date"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator"/>
        <result property="creationTime" column="creation_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modificationTime" column="modification_time"/>
        <result property="belongSystem" column="belong_system"/>
    </resultMap>

    <sql id="selectGdxxWorkOrderVo">
        select id,
               work_order_title,
               work_order_type,
               requester_id,
               requester_department_id,
               requirement_background,
               requirement_purpose,
               requirement_description,
               requirement_submission_time,
               acceptance_time,
               requirement_priority,
               expected_completion_date,
               requirement_remark,
               work_order_status,
               rnd_progress,
               requirement_implementation_system,
               system_function_module,
               project_risk,
               external_stakeholder_info,
               current_executor,
               requirement_schedule_start_date,
               requirement_schedule_end_date,
               design_schedule_start_date,
               design_schedule_end_date,
               development_schedule_start_date,
               development_schedule_end_date,
               testing_schedule_start_date,
               testing_schedule_end_date,
               acceptance_testing_schedule_start_date,
               acceptance_testing_schedule_end_date,
               expected_go_live_date,
               status,
               creator,
               creation_time,
               modifier,
               modification_time,
               belong_system
        from gdxx_work_order
    </sql>

    <select id="selectGdxxWorkOrderList" parameterType="GdxxWorkOrder" resultMap="GdxxWorkOrderResult">
        <include refid="selectGdxxWorkOrderVo"/>
        <where>
            <if test="workOrderTitle != null and workOrderTitle != ''">and work_order_title like concat('%',
                #{workOrderTitle}, '%')
            </if>
            <if test="workOrderType != null and workOrderType != ''">and work_order_type = #{workOrderType}</if>
            <if test="requesterId != null and requesterId != ''">and requester_id = #{requesterId}</if>
            <if test="requesterDepartmentId != null and requesterDepartmentId != ''">and requester_department_id =
                #{requesterDepartmentId}
            </if>
            <if test="requirementPriority != null and requirementPriority != ''">and requirement_priority =
                #{requirementPriority}
            </if>
            <if test="workOrderStatus != null and workOrderStatus != ''">and work_order_status = #{workOrderStatus}</if>
            <if test="rndProgress != null and rndProgress != ''">and rnd_progress = #{rndProgress}</if>
            <if test="requirementImplementationSystem != null and requirementImplementationSystem != ''">and
                requirement_implementation_system = #{requirementImplementationSystem}
            </if>
            <if test="projectRisk != null and projectRisk != ''">and project_risk = #{projectRisk}</if>
            <if test="currentExecutor != null and currentExecutor != ''">and current_executor = #{currentExecutor}</if>
            <if test="status != null and status != ''">and status = #{status}</if>
        </where>
    </select>

    <select id="selectGdxxWorkOrderById" parameterType="Long" resultMap="GdxxWorkOrderResult">
        <include refid="selectGdxxWorkOrderVo"/>
        where id = #{id} and status = 0
    </select>
    <select id="getWorkOrderIds" resultType="java.lang.Long">
        SELECT DISTINCT
        ( gwo.id )
        FROM
        gdxx_work_order gwo
        LEFT JOIN ( SELECT * FROM gdxx_work_order_file WHERE source_category = 'ORDER_TABLE_ZHUBIAO' ) gwof ON gwo.id = gwof.source_table_id
        LEFT JOIN ( SELECT * FROM gdxx_work_order_personnel WHERE source_first_category = 'ORDER_TABLE_ZHUBIAO' and status = 0) gwop ON gwo.id = gwop.source_table_id
        <where>
            gwo.status=0 and
            gwo.work_order_status !='ORDER_STATUS_CAOGAO'
            <if test="workOrderTitle != null and workOrderTitle != ''">and gwo.work_order_title
                like   concat('%',#{workOrderTitle} , '%')
            </if>
            <if test="requesterId != null and requesterId != ''">and gwo.requester_id = #{requesterId}</if>
            <if test="currentExecutor != null and currentExecutor != ''">and (gwop.personnel_id = #{currentExecutor}
            and gwop.source_second_category ='ORDER_RENYUAN_ZHIXINGREN')
            </if>
            <if test="workOrderStatus != null and workOrderStatus != ''">and gwo.work_order_status =
                #{workOrderStatus}
            </if>
            <if test="rndProgress != null and rndProgress != ''">and gwo.rnd_progress = #{rndProgress}</if>
            <if test="requirementPriority != null and requirementPriority != ''">and gwo.requirement_priority =
                #{requirementPriority}
            </if>
            <if test="requirementSubmissionTime != null ">and
                TO_days(gwo.requirement_submission_time) = to_days(#{requirementSubmissionTime})
            </if>
            <if test="isMeSubmmitFlag != null and isMeSubmmitFlag == '0'.toString() ">and gwo.requester_id = #{userId}
            </if>
            <if test="isMeJoinFlag != null and isMeJoinFlag == '0'.toString() ">and
                (
                gwo.requester_id = #{userId} or gwo.current_executor=#{userId} or gwop.personnel_id = #{userId}
                )
            </if>
            <if test="isDoingFlag != null and isDoingFlag == '0'.toString() ">and gwo.work_order_status in (
                'ORDER_STATUS_DAISHOULI','ORDER_STATUS_YISHOULI','ORDER_STATUS_DAIYANSHOU','ORDER_STATUS_YANSHOUTONGGUO'
                )
            </if>
            <if test="isDaiShouLiFlag != null and isDaiShouLiFlag == '0'.toString() ">and gwo.work_order_status =
                'ORDER_STATUS_DAISHOULI'
            </if>
            <if test="completeTime != null and completeTime != ''">and gwo.work_order_status =
                'ORDER_STATUS_YIWANCHENG' and to_days(#{completeTime}) = to_days(gwo.modification_time)
            </if>
            <if test="belongSystem != null and belongSystem != ''">
                and gwo.belong_system = #{belongSystem}
            </if>
            <if test="rndProgressList != null and rndProgressList.size() > 0">
                and gwo.rnd_progress in
                <foreach collection="rndProgressList" item="rndProgress" open="(" separator="," close=")">
                    #{rndProgress}
                </foreach>
            </if>
            <if test="workOrderStatusList != null and workOrderStatusList.size() > 0">
                and gwo.work_order_status in
                <foreach collection="workOrderStatusList" item="workOrderStatus" open="(" separator="," close=")">
                    #{workOrderStatus}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectGdxxWorkOrderListByIds" resultType="com.ruoyi.system.domain.GdxxWorkOrder">
        <include refid="selectGdxxWorkOrderVo"/>
        <where>
            id in
            <foreach item="id" collection="query" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        order by requirement_submission_time desc
    </select>
    <select id="selectGdxxWorkOrderListByIdsCount" resultType="java.lang.Long">
        select count(1) from gdxx_work_order
        <where>
            id in
            <foreach item="id" collection="query" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>
    <select id="getWorkOrderIdsInCaoGao" resultType="java.lang.Long">
        SELECT DISTINCT
        ( gwo.id )
        FROM
        gdxx_work_order gwo
        LEFT JOIN ( SELECT * FROM gdxx_work_order_file WHERE source_category = 'ORDER_TABLE_ZHUBIAO' ) gwof ON gwo.id = gwof.source_table_id
        LEFT JOIN ( SELECT * FROM gdxx_work_order_personnel WHERE source_first_category = 'ORDER_TABLE_ZHUBIAO' and status = 0) gwop ON gwo.id = gwop.source_table_id
        <where>
            gwo.status=0 and
            gwo.work_order_status ='ORDER_STATUS_CAOGAO' and gwo.requester_id = #{userId}
            <if test="workOrderTitle != null and workOrderTitle != ''">and gwo.work_order_title
                like   concat('%',#{workOrderTitle} , '%')
            </if>
            <if test="requesterId != null and requesterId != ''">and gwo.requester_id = #{requesterId}</if>
            <if test="currentExecutor != null and currentExecutor != ''">and (gwop.personnel_id = #{currentExecutor}
                and gwop.source_second_category ='ORDER_RENYUAN_ZHIXINGREN')
            </if>
            <if test="workOrderStatus != null and workOrderStatus != ''">and gwo.work_order_status =
                #{workOrderStatus}
            </if>
            <if test="rndProgress != null and rndProgress != ''">and gwo.rnd_progress = #{rndProgress}</if>
            <if test="requirementPriority != null and requirementPriority != ''">and gwo.requirement_priority =
                #{requirementPriority}
            </if>
            <if test="requirementSubmissionTime != null ">and
                TO_days(gwo.requirement_submission_time) = to_days(#{requirementSubmissionTime})
            </if>
            <if test="isMeSubmmitFlag != null and isMeSubmmitFlag == '0'.toString() ">and gwo.requester_id = #{userId}
            </if>
            <if test="isMeJoinFlag != null and isMeJoinFlag == '0'.toString() ">and
                (
                gwo.requester_id = #{userId} or gwo.current_executor=#{userId} or gwop.personnel_id = #{userId}
                )
            </if>
            <if test="isDoingFlag != null and isDoingFlag == '0'.toString() ">and gwo.work_order_status in (
                'ORDER_STATUS_DAISHOULI','ORDER_STATUS_YISHOULI','ORDER_STATUS_DAIYANSHOU','ORDER_STATUS_YANSHOUTONGGUO'
                )
            </if>
            <if test="isDaiShouLiFlag != null and isDaiShouLiFlag == '0'.toString() ">and gwo.work_order_status =
                'ORDER_STATUS_DAISHOULI'
            </if>
            <if test="completeTime != null and completeTime != ''">and gwo.work_order_status =
                'ORDER_STATUS_YIWANCHENG' and to_days(#{completeTime}) = to_days(gwo.modification_time)
            </if>
            <if test="belongSystem != null and belongSystem != ''">
                and gwo.belong_system = #{belongSystem}
            </if>
            <if test="rndProgressList != null and rndProgressList.size() > 0">
                and gwo.rnd_progress in
                <foreach collection="rndProgressList" item="rndProgress" open="(" separator="," close=")">
                    #{rndProgress}
                </foreach>
            </if>
            <if test="workOrderStatusList != null and workOrderStatusList.size() > 0">
                and gwo.work_order_status in
                <foreach collection="workOrderStatusList" item="workOrderStatus" open="(" separator="," close=")">
                    #{workOrderStatus}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="insertGdxxWorkOrder" parameterType="GdxxWorkOrder" useGeneratedKeys="true" keyProperty="id">
        insert into gdxx_work_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderTitle != null and workOrderTitle != ''">work_order_title,</if>
            <if test="workOrderType != null and workOrderType != ''">work_order_type,</if>
            <if test="requesterId != null and requesterId != ''">requester_id,</if>
            <if test="requesterDepartmentId != null and requesterDepartmentId != ''">requester_department_id,</if>
            <if test="requirementBackground != null and requirementBackground != ''">requirement_background,</if>
            <if test="requirementPurpose != null and requirementPurpose != ''">requirement_purpose,</if>
            <if test="requirementDescription != null and requirementDescription != ''">requirement_description,</if>
            <if test="requirementSubmissionTime != null">requirement_submission_time,</if>
            <if test="acceptanceTime != null">acceptance_time,</if>
            <if test="requirementPriority != null and requirementPriority != ''">requirement_priority,</if>
            <if test="expectedCompletionDate != null and expectedCompletionDate != ''">expected_completion_date,</if>
            <if test="requirementRemark != null and requirementRemark != ''">requirement_remark,</if>
            <if test="workOrderStatus != null and workOrderStatus != ''">work_order_status,</if>
            <if test="rndProgress != null and rndProgress != ''">rnd_progress,</if>
            <if test="requirementImplementationSystem != null and requirementImplementationSystem != ''">
                requirement_implementation_system,
            </if>
            <if test="systemFunctionModule != null and systemFunctionModule != ''">system_function_module,</if>
            <if test="projectRisk != null and projectRisk != ''">project_risk,</if>
            <if test="externalStakeholderInfo != null and externalStakeholderInfo != ''">external_stakeholder_info,</if>
            <if test="currentExecutor != null and currentExecutor != ''">current_executor,</if>
            <if test="requirementScheduleStartDate != null and requirementScheduleStartDate != ''">
                requirement_schedule_start_date,
            </if>
            <if test="requirementScheduleEndDate != null and requirementScheduleEndDate != ''">
                requirement_schedule_end_date,
            </if>
            <if test="designScheduleStartDate != null and designScheduleStartDate != ''">design_schedule_start_date,
            </if>
            <if test="designScheduleEndDate != null and designScheduleEndDate != ''">design_schedule_end_date,</if>
            <if test="developmentScheduleStartDate != null and developmentScheduleStartDate != ''">
                development_schedule_start_date,
            </if>
            <if test="developmentScheduleEndDate != null and developmentScheduleEndDate != ''">
                development_schedule_end_date,
            </if>
            <if test="testingScheduleStartDate != null and testingScheduleStartDate != ''">
                testing_schedule_start_date,
            </if>
            <if test="testingScheduleEndDate != null and testingScheduleEndDate != ''">testing_schedule_end_date,</if>
            <if test="acceptanceTestingScheduleStartDate != null and acceptanceTestingScheduleStartDate != ''">
                acceptance_testing_schedule_start_date,
            </if>
            <if test="acceptanceTestingScheduleEndDate != null and acceptanceTestingScheduleEndDate != ''">
                acceptance_testing_schedule_end_date,
            </if>
            <if test="expectedGoLiveDate != null and expectedGoLiveDate != ''">expected_go_live_date,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="modifier != null and modifier != ''">modifier,</if>
            <if test="modificationTime != null">modification_time,</if>
            <if test="belongSystem != null and belongSystem != ''">belong_system,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderTitle != null and workOrderTitle != ''">#{workOrderTitle},</if>
            <if test="workOrderType != null and workOrderType != ''">#{workOrderType},</if>
            <if test="requesterId != null and requesterId != ''">#{requesterId},</if>
            <if test="requesterDepartmentId != null and requesterDepartmentId != ''">#{requesterDepartmentId},</if>
            <if test="requirementBackground != null and requirementBackground != ''">#{requirementBackground},</if>
            <if test="requirementPurpose != null and requirementPurpose != ''">#{requirementPurpose},</if>
            <if test="requirementDescription != null and requirementDescription != ''">#{requirementDescription},</if>
            <if test="requirementSubmissionTime != null">#{requirementSubmissionTime},</if>
            <if test="acceptanceTime != null">#{acceptanceTime},</if>
            <if test="requirementPriority != null and requirementPriority != ''">#{requirementPriority},</if>
            <if test="expectedCompletionDate != null and expectedCompletionDate != ''">#{expectedCompletionDate},</if>
            <if test="requirementRemark != null and requirementRemark != ''">#{requirementRemark},</if>
            <if test="workOrderStatus != null and workOrderStatus != ''">#{workOrderStatus},</if>
            <if test="rndProgress != null and rndProgress != ''">#{rndProgress},</if>
            <if test="requirementImplementationSystem != null and requirementImplementationSystem != ''">
                #{requirementImplementationSystem},
            </if>
            <if test="systemFunctionModule != null and systemFunctionModule != ''">#{systemFunctionModule},</if>
            <if test="projectRisk != null and projectRisk != ''">#{projectRisk},</if>
            <if test="externalStakeholderInfo != null and externalStakeholderInfo != ''">#{externalStakeholderInfo},
            </if>
            <if test="currentExecutor != null and currentExecutor != ''">#{currentExecutor},</if>
            <if test="requirementScheduleStartDate != null and requirementScheduleStartDate != ''">
                #{requirementScheduleStartDate},
            </if>
            <if test="requirementScheduleEndDate != null and requirementScheduleEndDate != ''">
                #{requirementScheduleEndDate},
            </if>
            <if test="designScheduleStartDate != null and designScheduleStartDate != ''">#{designScheduleStartDate},
            </if>
            <if test="designScheduleEndDate != null and designScheduleEndDate != ''">#{designScheduleEndDate},</if>
            <if test="developmentScheduleStartDate != null and developmentScheduleStartDate != ''">
                #{developmentScheduleStartDate},
            </if>
            <if test="developmentScheduleEndDate != null and developmentScheduleEndDate != ''">
                #{developmentScheduleEndDate},
            </if>
            <if test="testingScheduleStartDate != null and testingScheduleStartDate != ''">
                #{testingScheduleStartDate},
            </if>
            <if test="testingScheduleEndDate != null and testingScheduleEndDate != ''">#{testingScheduleEndDate},</if>
            <if test="acceptanceTestingScheduleStartDate != null and acceptanceTestingScheduleStartDate != ''">
                #{acceptanceTestingScheduleStartDate},
            </if>
            <if test="acceptanceTestingScheduleEndDate != null and acceptanceTestingScheduleEndDate != ''">
                #{acceptanceTestingScheduleEndDate},
            </if>
            <if test="expectedGoLiveDate != null and expectedGoLiveDate != ''">#{expectedGoLiveDate},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="modifier != null and modifier != ''">#{modifier},</if>
            <if test="modificationTime != null">#{modificationTime},</if>
            <if test="belongSystem != null and belongSystem != ''">#{belongSystem},</if>
        </trim>
    </insert>

    <update id="updateGdxxWorkOrder" parameterType="GdxxWorkOrder">
        update gdxx_work_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderTitle != null">work_order_title = #{workOrderTitle},</if>
            <if test="workOrderType != null">work_order_type = #{workOrderType},</if>
            <if test="requesterId != null">requester_id = #{requesterId},</if>
            <if test="requesterDepartmentId != null">requester_department_id = #{requesterDepartmentId},</if>
            <if test="requirementBackground != null">requirement_background = #{requirementBackground},</if>
            <if test="requirementPurpose != null">requirement_purpose = #{requirementPurpose},</if>
            <if test="requirementDescription != null">requirement_description = #{requirementDescription},</if>
            <if test="requirementSubmissionTime != null">requirement_submission_time = #{requirementSubmissionTime},
            </if>
            <if test="acceptanceTime != null">acceptance_time = #{acceptanceTime},</if>
            <if test="requirementPriority != null">requirement_priority = #{requirementPriority},</if>
            <if test="expectedCompletionDate != null">expected_completion_date = #{expectedCompletionDate},</if>
            <if test="requirementRemark != null">requirement_remark = #{requirementRemark},</if>
            <if test="workOrderStatus != null">work_order_status = #{workOrderStatus},</if>
            <if test="rndProgress != null">rnd_progress = #{rndProgress},</if>
            <if test="requirementImplementationSystem != null">requirement_implementation_system =
                #{requirementImplementationSystem},
            </if>
            <if test="systemFunctionModule != null">system_function_module = #{systemFunctionModule},</if>
            <if test="projectRisk != null">project_risk = #{projectRisk},</if>
            <if test="externalStakeholderInfo != null">external_stakeholder_info = #{externalStakeholderInfo},</if>
            <if test="currentExecutor != null">current_executor = #{currentExecutor},</if>
            <if test="requirementScheduleStartDate != null">requirement_schedule_start_date =
                #{requirementScheduleStartDate},
            </if>
            <if test="requirementScheduleEndDate != null">requirement_schedule_end_date =
                #{requirementScheduleEndDate},
            </if>
            <if test="designScheduleStartDate != null">design_schedule_start_date = #{designScheduleStartDate},</if>
            <if test="designScheduleEndDate != null">design_schedule_end_date = #{designScheduleEndDate},</if>
            <if test="developmentScheduleStartDate != null">development_schedule_start_date =
                #{developmentScheduleStartDate},
            </if>
            <if test="developmentScheduleEndDate != null">development_schedule_end_date =
                #{developmentScheduleEndDate},
            </if>
            <if test="testingScheduleStartDate != null">testing_schedule_start_date = #{testingScheduleStartDate},</if>
            <if test="testingScheduleEndDate != null">testing_schedule_end_date = #{testingScheduleEndDate},</if>
            <if test="acceptanceTestingScheduleStartDate != null">acceptance_testing_schedule_start_date =
                #{acceptanceTestingScheduleStartDate},
            </if>
            <if test="acceptanceTestingScheduleEndDate != null">acceptance_testing_schedule_end_date =
                #{acceptanceTestingScheduleEndDate},
            </if>
            <if test="expectedGoLiveDate != null">expected_go_live_date = #{expectedGoLiveDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modificationTime != null">modification_time = #{modificationTime},</if>
            <if test="belongSystem != null">belong_system = #{belongSystem},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGdxxWorkOrderById" parameterType="Long">
        delete
        from gdxx_work_order
        where id = #{id}
    </delete>

    <delete id="deleteGdxxWorkOrderByIds" parameterType="String">
        delete from gdxx_work_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
