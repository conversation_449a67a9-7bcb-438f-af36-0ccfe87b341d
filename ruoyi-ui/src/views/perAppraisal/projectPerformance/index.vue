<template>
  <div class="p-5">
    <MyForm
      v-show="showSearch"
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    />
    <el-divider></el-divider>
    <div class="flex mb-2 justify-between">
      <div class="flex">
        <el-button
          v-hasPermi="['perAppraisal:projectPerformance:add']"
          @click="openProject = true"
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          >增加项目业绩</el-button
        >

        <el-button
          v-hasPermi="['perAppraisal:projectPerformance:import']"
          @click="handleImport"
          type="info"
          size="mini"
          plain
          icon="el-icon-upload2"
          >批量导入项目业绩</el-button
        >
        <el-button
          v-hasPermi="['perAppraisal:projectPerformance:submit']"
          @click="
            $router.push({
              path: '/perAppraisalOther/projectPerformanceReview',
            })
          "
          type="primary"
          size="mini"
          plain
          icon="el-icon-check"
        >
          提交项目业绩审核</el-button
        >
      </div>
    </div>
    <right-toolbar
      :showSearch.sync="showSearch"
      @queryTable="getList"
      @handleClose="handleClose"
      :columns="columnsChange"
      :propsTransfer="propsTransfer"
    ></right-toolbar>

    <MyTable :columns="columns" :source="configList">
      <template #yewuList="{ record }">
        <el-tooltip
          class="item"
          effect="dark"
          :content="
            record.yewuList && record.yewuList.map((item) => item.name).join()
          "
          placement="top-start"
        >
          <div class="truncate ...">
            {{
              record.yewuList && record.yewuList.map((item) => item.name).join()
            }}
          </div>
        </el-tooltip>
      </template>
      <template #status="{ record }">
        <div>{{ statusObj[record.state] }}</div>
      </template>
      <template #operate="{ record }">
        <el-button
          @click="goView(record, 'update')"
          type="text"
          v-show="['1', '4'].includes(record.state)"
          >修改</el-button
        >
        <el-button type="text" @click="goView(record, 'view')"
          >查看详情</el-button
        >

        <el-button
          @click="handleDelete(record)"
          type="text"
          v-show="['1', '4'].includes(record.state)"
          style="color: #f56c6c"
          >删除</el-button
        >
        <el-dropdown
          @command="handleCommand($event, record)"
          class="ml-2"
          v-show="['2', '3'].includes(record.state)"
        >
          <span class="el-dropdown-link mr-2" style="font-size: 14px">
            >>更多
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="records">查看修改记录</el-dropdown-item>
            <el-dropdown-item command="process">查看最新流程</el-dropdown-item>
            <el-dropdown-item
              command="submitReview"
              v-show="['3'].includes(record.state)"
              >修改并提交审核</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </MyTable>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <UploadImportant
      v-model="openImport"
      @on-save-success="getList"
      :url="UploadImporUrl"
    />
    <DetailDialog v-model="openProject" />
    <RecordsDialog v-model="openRecord" :recordForm="recordForm" />
  </div>
</template>

<script>
import DetailDialog from "./components/DetailDialog.vue";
import RecordsDialog from "./components/RecordsDialog.vue";
import { haveAuthorityCompanyList } from "@/api/businessInformation/companyInformation";
import {
  listEnter,
  delAchievementEnter,
  enterList,
  replaceColumnsInit,
  getSelectListOfAchievement,
} from "@/api/perAppraisal/projectPerformance";
import config from "./components/config";
import XEUtils from "xe-utils";

export default {
  name: "ProjectPerformance",
  components: { DetailDialog, RecordsDialog },

  data() {
    return {
      ...config,
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        year: [new Date().getFullYear() + ""],
      },
      total: 0,
      configList: [],
      openImport: false,
      openProject: false,
      openRecord: false,
      recordForm: {},
    };
  },

  created() {
    this.init();
  },
  methods: {
    init() {
      this.getProjectList();
      this.getCompanyList();
      this.getColumnsInit();
      this.getList();
    },
    async getProjectList() {
      const { rows } = await getSelectListOfAchievement();
      this.formColumns[0].options = rows;
      this.formColumnsDialog[0].options = rows;
    },
    async getCompanyList() {
      const { rows } = await haveAuthorityCompanyList({ isInside: 1 });
      this.formColumns[1].options = rows;
    },
    async getColumnsInit() {
      //发送请求获取列 如果没有则用默认值
      const { rows } = await enterList();
      rows.forEach((item) => {
        this.columnsAddInit.forEach((item1) => {
          if (item.columns == item1.columns) {
            this.$set(item, "label", item1.label);
          }
        });
      });
      this.columnsChange = rows;
      this.columns = this.columnsInit.concat(
        this.getColumnsChange(rows),
        this.columnsOperate
      );
    },
    getColumnsChange(rows) {
      const showColumns = rows
        .filter((item) => item.visible)
        .map((item) => item.columns);
      return this.columnsAddInit.filter((item) => {
        return showColumns.includes(item.columns);
      });
    },
    async getList() {
      const { rows, total } = await listEnter(this.getParams());
      this.configList = rows;
      this.total = total;
    },
    getParams() {
      const params = XEUtils.clone(this.queryParams, true);
      params.years = params.year?.map((item) => (item = Number(item))).join();
      params.companyIds = params.companyId
        ?.map((item) => (item = Number(item)))
        .join();
      params.projectIds = params.projectId
        ?.map((item) => (item = Number(item)))
        .join();

      delete params.year;
      delete params.companyId;
      delete params.projectId;
      return params;
    },
    async handleClose() {
      this.columns = this.columnsInit.concat(
        this.getColumnsChange(this.columnsChange),
        this.columnsOperate
      );
      await replaceColumnsInit(this.columnsChange);
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    handleImport() {
      this.openImport = true;
    },
    handleExport() {
      this.download(
        "annual/plan/export",
        this.getParams(),
        `项目业绩录入.xlsx`
      );
    },

    handleCommand(command, record) {
      const obj = {
        records: this.records,
        process: this.process,
        submitReview: this.submitReview,
      };
      obj[command](record);
    },

    records(record) {
      this.recordForm = record;
      this.openRecord = true;
    },
    process(record) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: record.processId,
          businessId: record.processId,
        },
      });
    },
    submitReview(record) {
      this.goView(record, "update", true);
    },
    handleDelete(row) {
      let text = `您确定删除${row.projectName}项目【${row.year}】年度的数据吗？`;
      this.$modal
        .confirm(text)
        .then(function () {
          return delAchievementEnter(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    goView(row, type, isPass = false) {
      const title = {
        update: "修改项目业绩",
        view: "查看项目业绩详情",
      }[type];
      const form = {
        year: row.year,
        projectId: row.projectId,
        isPass,
      };
      this.$router.push({
        path: `/perAppraisalOther/projectPerformance/${row.id}`,
        query: {
          title,
          form: JSON.stringify(form),
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-table {
  .el-table__header-wrapper {
    table {
      thead {
        th {
          font-weight: bold;
          color: #333;
          // 换行
          .cell {
            white-space: pre-wrap;
          }
        }
      }
    }
  }
}
</style>
