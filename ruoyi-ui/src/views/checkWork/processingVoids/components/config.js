export default {
  applicationTypeList: Object.freeze([
    { label: "请假", value: "1" },
    { label: "加班", value: "2" },
    { label: "奖惩", value: "3" },
    { label: "出差", value: "4" },
  ]),
  applicationTypeMap: Object.freeze({
    1: "请假",
    2: "加班",
    3: "奖惩",
    4: "出差",
  }),
  pickerOptions: Object.freeze({
    shortcuts: [
      {
        text: "最近一周",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近一个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近三个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          picker.$emit("pick", [start, end]);
        },
      },
    ],
  }),
};
