export default {
  header: Object.freeze({
    title: "线下还款",
    content: [
      "数据来源：智慧财务系统-科目余额表",
      "收到线下还款：科目余额表中的  (负债类-其他应付款贷方发生额)",
      "支付线下还款：科目余额表中的  (负债类-其他应付款借方发生额)",
    ],
  }),
  columns: Object.freeze([
    { label: "项目名称", prop: "projectName",width: "600px", },
    { label: "期初金额", prop: "initialAmt",width: "200px", },

    {
      label: "收到线下还款",
      prop: "receiveAmt",
      isHSlot: true,
      width: "200px",
    },
    {
      label: "支付线下还款",
      prop: "payAmt",
      isHSlot: true,
      width: "200px",
    },
    { label: "余额", prop: "balanceAmt",width: "200px", },

    {
      label: "操作",
      key: "opertion",
      width: "100px",
    },
  ]),
  columnsProjectDetails: Object.freeze([
    { label: "记账日期", prop: "dataDay" },
    {
      label: "收到线下还款",
      prop: "receiveAmt",
    },
    {
      label: "支付线下还款",
      prop: "payAmt",
    },
  ]),
  pickerOptions: Object.freeze({
    shortcuts: [
      {
        text: "最近一周",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近一个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近三个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          picker.$emit("pick", [start, end]);
        },
      },
    ],
  }),
};
