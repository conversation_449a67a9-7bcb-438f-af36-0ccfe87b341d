<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="提示"
      :visible.sync="innerValue"
      width="500px"
      @close="handleClose"
      @open="handleOpen"
    >
      <el-scrollbar>
        <div class="content">
          <div v-for="(item,index) in table" :key="index">
            [ {{item.licenseName}} ]
          </div>
          <div style="margin-top:10px"> 请检查以上证照是否已全部收回</div>
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button @click="sure" type="primary">确认</el-button>
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
      
    </el-dialog>
  </div>
</template>
<script>

import { pendingDetailDetail } from "@/api/certificate/pendingProcessing";

export default {
  props: ["value","table","isProcess"],


  computed: {
    innerValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },

  data() {
    return {
      
    
    };
  },
  watch: {},
  mounted() {},
  methods: {
    async init() {
    
    },
    async sure(){
      const params={
        businessId:this.table[0].businessId,
        signStatus:5,
      
      }
      await pendingDetailDetail(params);
     if(!this.isProcess){
          this.$message({
          message: '操作成功',
          type: 'success'
        });
        }
      this.innerValue=false;
      this.$emit('save','takeBack');
    },
    handleOpen() {
      this.init();
    },
    handleClose() {},
  },
};
</script>

<style lang="less" scoped>
.content {
  max-height: 70vh;
  padding-right: 20px;
}
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>