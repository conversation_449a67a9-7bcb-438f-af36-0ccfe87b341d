<template>
    <div>
        <el-dialog title="工单详情" :visible.sync="dialogVisible" width="950px" :before-close="handleClose">
            <div style="display: flex;" v-if="form.workOrderType">
                <div style="width: 60%;flex-shrink: 0;border-right: 1px solid #dcdfe6;">
                    <el-form ref="form" :model="form" label-width="130px" :rules="rules">
                        <!-- 工单主题 -->
                        <el-form-item label="工单ID">
                          {{ form.id }}
                        </el-form-item>
                        <div style="display: flex;">
                            <el-form-item label="工单主题">
                                {{ form.workOrderTitle }}
                            </el-form-item>
                            <el-form-item label="所属系统">
                                {{$store.state.data.KV_MAP.gdxx_belong_system[form.belongSystem] }}
                            </el-form-item>
                        </div>
                        <div style="display: flex;">
                            <el-form-item label="工单类型">
                                {{form.workOrderType ? dict.type.work_order_type.find(item => item.value ===
                                    form.workOrderType).label : '-'
                                }}

                            </el-form-item>
                            <el-form-item label="需求提出人部门">
                                {{ form.requesterDepartmentName }}

                            </el-form-item>
                        </div>
                        <el-form-item label="需求背景">
                            {{ form.requirementBackground }}

                        </el-form-item>
                        <el-form-item label="需求目的">
                            {{ form.requirementPurpose }}

                        </el-form-item>
                        <el-form-item label="需求描述">
                            {{ form.requirementDescription }}

                        </el-form-item>

                        <div style="display: flex;">
                            <el-form-item label="期望完成时间">
                                {{ form.expectedCompletionDate }}

                            </el-form-item>
                            <el-form-item label="需求优先级">
                                {{dict.type.work_order_xuqiu_type.find(item => item.value ===
                                    form.requirementPriority).label
                                }}


                            </el-form-item>
                        </div>
                        <el-form-item label="需求备注">
                            {{ form.requirementRemark }}

                        </el-form-item>
                        <el-divider></el-divider>
                        <div style="display: flex;">
                            <el-form-item label="工单状态">

                                {{dict.type.work_order_status_type.find(item => item.value ===
                                    form.workOrderStatus).label
                                }}
                            </el-form-item>
                            <el-form-item label="研发进度">
                                {{form.rndProgress ? dict.type.work_order_yanfa_jindu_type.find(item => item.value
                                    ===
                                    form.rndProgress).label : '-'
                                }}

                            </el-form-item>
                        </div>
                        <div style="display: flex;">
                            <el-form-item label="需求实现系统">
                                {{form.requirementImplementationSystem ?
                                    dict.type.work_order_shixian_system.find(item =>
                                        item.value ===
                                        form.requirementImplementationSystem).label : '-'
                                }}

                            </el-form-item>
                            <el-form-item label="系统功能模块">
                                {{ form.systemFunctionModule }}

                            </el-form-item>
                        </div>
                        <el-form-item label="项目风险">
                            {{form.projectRisk ? dict.type.work_order_project_fengxian.find(item => item.value ===
                                form.projectRisk).label : '-'
                            }}

                        </el-form-item>
                        <div style="display: flex;" v-for="(item, index) in xqUsers" :key="index">
                            <el-form-item label="需求涉及部门">
                                {{ item.departmentName }}
                            </el-form-item>
                            <el-form-item label="部门需求负责人">
                                {{ item.personnelName }}

                            </el-form-item>
                        </div>
                        <el-form-item label="需求外部相关人">
                            {{ form.externalStakeholderInfo }}

                        </el-form-item>
                        <el-divider></el-divider>
                        <div style="display: flex;">
                            <el-form-item label="当前执行人">
                                {{ zxNames }}

                            </el-form-item>
                            <el-form-item label="工单负责人">
                                {{ fzrNames }}

                            </el-form-item>
                        </div>
                        <div style="display: flex;">
                            <el-form-item label="产品经理">
                                {{ cpNames }}

                            </el-form-item>
                            <el-form-item label="项目经理">
                                {{ xmNames }}

                            </el-form-item>
                        </div>
                        <div style="display: flex;">
                            <el-form-item label="开发人员">
                                {{ kfNames }}

                            </el-form-item>
                            <el-form-item label="测试人员">
                                {{ csNames }}

                            </el-form-item>
                        </div>
                        <el-form-item label="需求排期">
                            {{ xqDate.length > 0 ? xqDate[0] + "至" + xqDate[1] : "" }}

                        </el-form-item>
                        <el-form-item label="设计排期">
                            {{ sjDate.length > 0 ? sjDate[0] + "至" + sjDate[1] : "" }}

                        </el-form-item>
                        <el-form-item label="开发排期">
                            {{ kfDate.length > 0 ? kfDate[0] + "至" + kfDate[1] : "" }}

                        </el-form-item>
                        <el-form-item label="测试排期">
                            {{ csDate.length > 0 ? csDate[0] + "至" + csDate[1] : "" }}

                        </el-form-item>
                        <el-form-item label="验收测试排期">
                            {{ ysDate.length > 0 ? ysDate[0] + "至" + ysDate[1] : "" }}

                        </el-form-item>
                        <el-form-item label="预计上线时间">
                            {{ form.expectedGoLiveDate }}

                        </el-form-item>
                        <!-- 附件上传 -->
                        <el-form-item label="需求附件">
                            <el-upload disabled class="upload-demo" :auto-upload="false" action
                                :on-preview="handlePreview" :on-success="handleFileSuccess"
                                :before-upload="beforeUpload" :before-remove="beforeRemove" :on-change="handleChange"
                                :on-remove="handleRemove" :on-progress="handleFileUploadProgress" multiple
                                :file-list="fileList">

                            </el-upload>
                        </el-form-item>

                    </el-form>
                </div>
                <div style="flex: 1;padding: 0 14px;">
                    <span style="font-weight: bold;margin-right: 10px;">工单动态</span>
                    <el-button type="primary" @click="sendType = true"
                        v-hasPermi="[type + ':sendDynamics']">发布工单动态</el-button>
                    <el-timeline :reverse="true">
                        <el-timeline-item v-for="(activity, index) in timeData" :key="index"
                            :timestamp="$format(activity.creationTime, 'yyyy-MM-dd HH:mm:ss')">
                            {{activity.creatorName + '：' +
                                (activity.dynamicType ? dict.type.work_order_dongtai_type.find(item => item.value
                                    ===
                                    activity.dynamicType).label : '')}}
                            <span v-if="activity.dynamicType == 'ORDER_DONGTAI_FABUDONGTAI'">：</span>
                            <span v-if="activity.dynamicContent">{{ activity.dynamicContent }}</span>
                            <el-button @click="getHistoryDetail(activity)" style="margin-left: 6px;" type="text"
                                v-if="['ORDER_DONGTAI_SHOULIGONGDAN', 'ORDER_DONGTAI_XIUGAIGONGDAN', 'ORDER_DONGTAI_CHUANGJIANGONGDAN','ORDER_RENYUAN_GONGDANFUZEREN'].includes(activity.dynamicType)">查看</el-button>
                            <div v-if="activity.fileList.length > 0">
                                <div v-for="(item, index) in activity.fileList" :key="index"><el-button
                                        style="white-space: pre-wrap" @click="handlePreview(item)" type="text">{{
                                            item.fileName }}</el-button></div>
                            </div>
                        </el-timeline-item>
                    </el-timeline>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div slot="footer" class="dialog-footer" v-if="type != 'workOrder'">
                <el-button v-hasPermi="[type + ':editStatusSl']"
                    v-show="form.workOrderStatus === 'ORDER_STATUS_DAISHOULI'" type="primary"
                    @click="submitForm('accept')">受理</el-button>
                <el-button v-hasPermi="[type + ':editStatusTjys']"
                    v-show="form.workOrderStatus === 'ORDER_STATUS_YISHOULI'" type="primary"
                    @click="submitForm('submitAccept')">提交验收</el-button>
                <el-button v-hasPermi="[type + ':editStatusYstg']"
                    v-show="form.workOrderStatus === 'ORDER_STATUS_DAIYANSHOU' && detailData.personnelList.filter(item => item.sourceSecondCategory === 'ORDER_RENYUAN_ZHIXINGREN').map(v => v.personnelId).includes(userId + '')"
                    type="primary" @click="submitForm('acceptPass')">验收通过</el-button>
                <el-button v-hasPermi="[type + ':editStatusXqwc']"
                    v-show="form.workOrderStatus === 'ORDER_STATUS_YANSHOUTONGGUO'" type="primary"
                    @click="submitForm('complete')">需求完成</el-button>
                <el-button v-hasPermi="[type + ':editStatusBjcg']"
                    v-show="form.workOrderStatus === 'ORDER_STATUS_CAOGAO' && detailData.requesterId == userId"
                    @click="submitForm('editDraft')">编辑草稿</el-button>
                <el-button v-hasPermi="[type + ':edit']"
                    v-show="['ORDER_STATUS_YISHOULI', 'ORDER_STATUS_DAIYANSHOU', 'ORDER_STATUS_YANSHOUTONGGUO'].includes(form.workOrderStatus)"
                    @click="submitForm('edit')">修改工单</el-button>
                <el-button v-hasPermi="[type + ':editStatusQxxq']"
                    v-show="['ORDER_STATUS_DAISHOULI', 'ORDER_STATUS_YISHOULI', 'ORDER_STATUS_DAIYANSHOU', 'ORDER_STATUS_YANSHOUTONGGUO'].includes(form.workOrderStatus)"
                    @click="submitForm('cencle')">取消需求</el-button>
                <el-button v-hasPermi="[type + ':editStatusZtxq']"
                    v-show="['ORDER_STATUS_DAISHOULI', 'ORDER_STATUS_YISHOULI', 'ORDER_STATUS_DAIYANSHOU', 'ORDER_STATUS_YANSHOUTONGGUO'].includes(form.workOrderStatus)"
                    @click="submitForm('stop')">暂停需求</el-button>
                <el-button v-hasPermi="[type + ':editStatusHfzt']"
                    v-show="form.workOrderStatus === 'ORDER_STATUS_YIZHANTING'"
                    @click="submitForm('restore')">恢复暂停</el-button>
                <el-button @click="handleClose">关闭</el-button>


            </div>
            <div slot="footer" class="dialog-footer" v-else>
                <el-button v-hasPermi="[type + ':editStatusSl']"
                    v-show="form.workOrderStatus === 'ORDER_STATUS_DAISHOULI'" type="primary"
                    @click="submitForm('accept')">受理</el-button>
                <el-button v-hasPermi="[type + ':editStatusTjys']"
                    v-show="form.workOrderStatus === 'ORDER_STATUS_YISHOULI'" type="primary"
                    @click="submitForm('submitAccept')">提交验收</el-button>
                <el-button v-hasPermi="[type + ':editStatusYstg']"
                    v-show="form.workOrderStatus === 'ORDER_STATUS_DAIYANSHOU'" type="primary"
                    @click="submitForm('acceptPass')">验收通过</el-button>
                <el-button v-hasPermi="[type + ':editStatusXqwc']"
                    v-show="form.workOrderStatus === 'ORDER_STATUS_YANSHOUTONGGUO'" type="primary"
                    @click="submitForm('complete')">需求完成</el-button>
                <el-button v-hasPermi="[type + ':editStatusBjcg']"
                    v-show="form.workOrderStatus === 'ORDER_STATUS_CAOGAO' && detailData.requesterId == userId"
                    @click="submitForm('editDraft')">编辑草稿</el-button>
                <el-button v-hasPermi="[type + ':edit']"
                    v-show="['ORDER_STATUS_YISHOULI', 'ORDER_STATUS_DAIYANSHOU', 'ORDER_STATUS_YANSHOUTONGGUO'].includes(form.workOrderStatus)"
                    @click="submitForm('edit')">修改工单</el-button>
                <el-button v-hasPermi="[type + ':editStatusQxxq']"
                    v-show="['ORDER_STATUS_DAISHOULI', 'ORDER_STATUS_YISHOULI', 'ORDER_STATUS_DAIYANSHOU', 'ORDER_STATUS_YANSHOUTONGGUO'].includes(form.workOrderStatus)"
                    @click="submitForm('cencle')">取消需求</el-button>
                <el-button v-hasPermi="[type + ':editStatusZtxq']"
                    v-show="['ORDER_STATUS_DAISHOULI', 'ORDER_STATUS_YISHOULI', 'ORDER_STATUS_DAIYANSHOU', 'ORDER_STATUS_YANSHOUTONGGUO'].includes(form.workOrderStatus)"
                    @click="submitForm('stop')">暂停需求</el-button>
                <el-button v-hasPermi="[type + ':editStatusHfzt']"
                    v-show="form.workOrderStatus === 'ORDER_STATUS_YIZHANTING'"
                    @click="submitForm('restore')">恢复暂停</el-button>
                <el-button @click="handleClose">关闭</el-button>


            </div>
            <el-image ref="previewImg" v-show="false" :src="photoUrl" :preview-src-list="imagePreviewUrls"></el-image>
        </el-dialog>
        <UserDepPostSelect :title="userDep.title" :multiple="userDep.multiple" :rowKey="userDep.rowKey"
            :multipleSelectionProp="userDep.multipleSelectionUserDep" v-model="userDep.open"
            @on-submit-success-user="userSuccess" @on-submit-success-dep="depSuccess" />
        <SendDynamic v-if="sendType" @close="close" :id="Id" />
        <HistoryDetail v-if="historyType" @close="close" :itemData="historyData" />
        <EditWorkOrder v-if="editType" @close="editType = false" @success="successEdit" :itemData="detailData" />
        <AddWorkOrder v-if="addType" @close="close" :itemData="detailData" />
    </div>
</template>

<script>
import { checkPermi } from "@/utils/permission"; // 权限判断函数
import { formRequest } from '@/utils/request'
import { getToken } from "@/utils/auth";
import { getDicts } from "@/api/system/dict/data";
import { workOrderDynamic, historyDetail, newDetail } from "@/api/workOrder/workOrder";
import SendDynamic from './SendDynamic.vue';
import HistoryDetail from './HistoryDetail.vue';
import { downloadByUrl } from "@/api/oa/processTemplate";

import { getFilesPathMapping } from "@/api/cdlb/files";
import EditWorkOrder from './EditWorkOrder.vue'
import AddWorkOrder from './AddWorkOrder.vue'


export default {
    dicts: ['work_order_type', "work_order_xuqiu_type", "work_order_status_type", 'work_order_yanfa_jindu_type', 'work_order_shixian_system', 'work_order_project_fengxian', 'work_order_dongtai_type', 'work_order_renyuan_type',],
    components: {
        SendDynamic,
        EditWorkOrder,
        AddWorkOrder,
        HistoryDetail
    },
    props: {
        itemData: Object, // 编辑数据
        type: String
    },
    data() {
        return {
            addType: false,
            editType: false,
            historyType: false,
            timeData: [],
            sendType: false,
            xqDate: [],
            sjDate: [],
            kfDate: [],
            csDate: [],
            ysDate: [],
            cpNames: '',
            zxNames: '',
            fzrNames:'',
            xmNames: '',
            kfNames: '',
            csNames: '',
            cpUsers: [],
            fzrUsers: [],
            xmUsers: [],
            kfUsers: [],
            csUsers: [],
            zxUsers: [],
            xqUsers: [{ departmentId: '', departmentName: '', personnelId: '', personnelName: '', sourceFirstCategory: '', sourceSecondCategory: '' }],
            userDep: {
                title: '',
                multiple: false,
                rowKey: '',
                open: false,
                multipleSelectionUserDep: []
            },
            rules: {
                workOrderTitle: [{ required: true, message: '工单主题不能为空', trigger: 'blur' }],
                currentExecutorName: [{ required: true, message: '当前执行人不能为空', trigger: 'change' }],

                workOrderType: [{ required: true, message: '请选择工单类型', trigger: 'change' }],
                requesterDepartmentName: [{ required: true, message: '请选择提出部门', trigger: 'change' }],
                requirementBackground: [{ required: true, message: '需求背景不能为空', trigger: 'blur' }],
                requirementPurpose: [{ required: true, message: '需求目的不能为空', trigger: 'blur' }],
                requirementDescription: [{ required: true, message: '需求描述不能为空', trigger: 'blur' }],
                requirementPriority: [{ required: true, message: '请选择优先级', trigger: 'change' }]
            },
            imagePreviewUrls: [],
            photoUrl: "",
            fileList: [],
            upload: {
                headers: { Authorization: "Bearer " + getToken() },
                // 上传的地址
                url: process.env.VUE_APP_BASE_API + "/lyxSystem/dayCheck/lyxUploadFile",
                //当前步骤id
                stepId: null,
                businessId: null,
            },
            dialogVisible: true,
            form: {
                currentExecutorName: '',
                workOrderTitle: '',
                belongSystem: '',
                workOrderType: '',
                requesterDepartmentName: '',
                requesterDepartmentId: '',
                requirementBackground: '',
                requirementPurpose: '',
                requirementDescription: '',
                expectedCompletionDate: '',
                requirementPriority: '',
                requirementRemark: '',
                departmentPersonnelListJson: [],
            },

            deptOptions: [], // 从接口获取的部门数据
            selectDetType: null,
            listIndex: null,
            typeOptions: [],
            xuqiuOptions: [],
            statusOptions: [],
            jinduOptions: [],
            systemOptions: [],
            fengxianOptions: [],
            historyData: null,
            detailData: null,
            Id: '',
            userId: '',
        };
    },
    mounted() {
        console.log(this.type);

        this.userId = sessionStorage.getItem('userId')
        if (this.itemData) {
            this.Id = this.itemData.id;
        }

        this.getDetail(this.Id)
    },
    methods: {
        checkPermi,
        getHistoryDetail(v) {
            historyDetail(v.wordOrderHistoryId).then(res => {
                this.historyData = res
                this.historyType = true
            })
        },
        successEdit(id) {
            console.log(id);

            this.Id = id ? id : this.Id
            this.editType = false
            this.getDetail(this.Id)
        },
        close(v) {
            this.historyType = false
            this.sendType = false
            this.addType = false
            if (v) {
                this.getDetail(v)
            } else {
                this.getDetail(this.Id)
            }


        },
        getDetail(id) {
            workOrderDynamic(id).then(res => {
                this.timeData = res.data ? res.data : []
                if (this.timeData.length > 0) {
                    this.timeData.forEach(item => {
                        if (item.fileList && item.fileList.length > 0) {
                            item.fileList.forEach(item => {
                                item.url = item.fileAddress
                                item.name = item.fileName
                            })
                        }
                    })
                }
            })
            newDetail(id).then(res => {
                this.detailData = res
                this.form.workOrderTitle = res.workOrderTitle
                this.form.belongSystem = res.belongSystem
                this.form.workOrderType = res.workOrderType
                this.form.requesterDepartmentName = res.requesterDepartmentName
                this.form.requesterDepartmentId = res.requesterDepartmentId
                this.form.requirementBackground = res.requirementBackground
                this.form.requirementPurpose = res.requirementPurpose
                this.form.requirementDescription = res.requirementDescription
                this.form.expectedCompletionDate = res.expectedCompletionDate
                this.form.requirementPriority = res.requirementPriority
                this.form.requirementRemark = res.requirementRemark
                this.form.workOrderStatus = res.workOrderStatus
                this.form.currentExecutor = res.currentExecutor
                this.form.currentExecutorName = res.currentExecutorName
                this.form.requirementImplementationSystem = res.requirementImplementationSystem
                this.form.rndProgress = res.rndProgress
                this.form.systemFunctionModule = res.systemFunctionModule
                this.form.projectRisk = res.projectRisk
                this.form.externalStakeholderInfo = res.externalStakeholderInfo
                this.form.expectedGoLiveDate = res.expectedGoLiveDate
                this.xqDate = res.requirementScheduleStartDate ? [res.requirementScheduleStartDate, res.requirementScheduleEndDate] : []
                this.sjDate = res.designScheduleStartDate ? [res.designScheduleStartDate, res.designScheduleEndDate] : []
                this.kfDate = res.developmentScheduleStartDate ? [res.developmentScheduleStartDate, res.developmentScheduleEndDate] : []
                this.csDate = res.testingScheduleStartDate ? [res.testingScheduleStartDate, res.testingScheduleEndDate] : []
                this.ysDate = res.acceptanceTestingScheduleStartDate ? [res.acceptanceTestingScheduleStartDate, res.acceptanceTestingScheduleEndDate] : []
                this.form.id = res.id
                if (res.fileList && res.fileList.length > 0) {
                    this.form.fileIds = res.fileList.map(item => { return item.id })
                }
                if (res.personnelList && res.personnelList.length > 0) {
                    res.personnelList.forEach(item => {
                        item.deptId = item.departmentId
                        item.deptName = item.departmentName
                        item.userId = item.personnelId
                        item.nickName = item.personnelName
                    })
                    this.zxUsers = res.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_ZHIXINGREN')
                    this.zxNames = this.zxUsers.map(item => { return item.nickName }).join(',')
                    this.cpUsers = res.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_CHANPINJINGLI')
                    this.cpNames = this.cpUsers.map(item => { return item.nickName }).join(',')
                    this.fzrUsers = res.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_GONGDANFUZEREN')
                    this.fzrNames = this.fzrUsers.map(item => { return item.nickName }).join(',')
                    this.xmUsers = res.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_XIANGMUJINGLI')
                    this.xmNames = this.xmUsers.map(item => { return item.nickName }).join(',')
                    this.kfUsers = res.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_KAIFARENYUAN')
                    this.kfNames = this.kfUsers.map(item => { return item.nickName }).join(',')
                    this.csUsers = res.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_CESHIRENYUAN')
                    this.csNames = this.csUsers.map(item => { return item.nickName }).join(',')
                    this.xqUsers = res.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_BUMENXUQIUFUZEREN').length > 0 ? this.itemData.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_BUMENXUQIUFUZEREN') : [{ departmentId: '', departmentName: '', personnelId: '', personnelName: '', sourceFirstCategory: '', sourceSecondCategory: '' }]
                }
                this.fileList = res.fileList.map(item => { return { name: item.fileName, url: item.fileAddress, id: item.id } })
            })



        },
        selectPostUser(e) {
            this.selectPostIndex = e
            this.listIndex = null
            console.log(this.listIndex);

            this.userDep.multiple = e == 0 ? false : true
            this.userDep.title = 'user'
            this.userDep.rowKey = 'userId'
            this.userDep.open = true
        },

        selectUser(index) {
            this.selectPostIndex = null
            this.listIndex = index
            this.userDep.title = 'user'
            this.userDep.rowKey = 'userId'
            this.userDep.open = true
        },
        selectDep(e, index) {
            this.listIndex = index
            this.selectDetType = e
            this.userDep.title = 'dep'
            this.userDep.rowKey = 'deptId'
            this.userDep.open = true
        },
        userSuccess(v) {
            console.log(v);
            console.log(this.listIndex);

            if (this.listIndex !== null && this.listIndex !== undefined) {
                this.xqUsers[this.listIndex].personnelName = v[0].nickName
                this.xqUsers[this.listIndex].personnelId = !v[0].userId ? '' : v[0].userId + ''
            } else {
                switch (this.selectPostIndex) {
                    case 0:
                        this.form.currentExecutorName = v[0].nickName
                        this.form.currentExecutor = v[0].userId + ''
                        this.$refs.form.validateField('currentExecutorName')
                        break;
                    case 1:
                        this.cpNames = v.map(item => { return item.nickName }).join(',')
                        this.cpUsers = v
                        break;
                    case 2:
                        this.xmNames = v.map(item => { return item.nickName }).join(',')
                        this.xmUsers = v
                        break;
                    case 3:
                        this.kfNames = v.map(item => { return item.nickName }).join(',')
                        this.kfUsers = v
                        break;
                    case 4:
                        this.csNames = v.map(item => { return item.nickName }).join(',')
                        this.csUsers = v
                        break;

                }
            }

        },
        depSuccess(v) {
            console.log(v);
            if (!this.selectDetType) {
                this.form.requesterDepartmentName = v[0].deptName
                this.form.requesterDepartmentId = v[0].deptId + ''
                this.$refs.form.validateField('requesterDepartmentName')
            } else {
                this.xqUsers[this.listIndex].departmentName = v[0].deptName
                this.xqUsers[this.listIndex].departmentId = v[0].deptId + ''
            }
        },
        addDept() {
            this.xqUsers.push(
                {
                    departmentId: '', departmentName: '', personnelId: '', personnelName: '', sourceFirstCategory: '', sourceSecondCategory: ''
                })
        },
        removeDept(index) {
            this.xqUsers.splice(index, 1)
        },
        submitForm(e) {
            switch (e) {
                case 'submit':

                    break;
                case 'accept':
                    this.$confirm('是否确认受理此工单？受理后工单将变为已受理状态，并转至工单编辑界面?', '受理工单', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.handleAccept(this.detailData)
                    })


                    break;
                case 'submitAccept':
                    this.$confirm('请确定此工单需求已完成，提交验收后，需要由当前执行人点击[验收通过]，项目经理才能完成此工单', '提交验收', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        formRequest(`/system/workOrder/updateStatus`, {
                            workOrderId: this.Id,
                            dynamicType: 'ORDER_STATUS_DAIYANSHOU'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('操作成功');
                            formRequest('/system/workOrder/dynamic/publish', {
                                operatorId: sessionStorage.getItem('userId'),
                                dynamicContent: '更新工单状态从[受理]到[提交验收]',
                                workOrderMainId: this.Id,
                            })
                            this.getDetail(this.Id)
                        })
                    })

                    break;
                case 'acceptPass':
                    this.$confirm('请确定此工单需求已通过验收，验收通过后，需要由项目经理点击[需求完成]结束此工单', '验收通过', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        formRequest(`/system/workOrder/updateStatus`, {
                            workOrderId: this.Id,
                            dynamicType: 'ORDER_STATUS_YANSHOUTONGGUO'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('操作成功');
                            formRequest('/system/workOrder/dynamic/publish', {
                                operatorId: sessionStorage.getItem('userId'),
                                dynamicContent: '更新工单状态从[待验收]到[验收通过]',
                                workOrderMainId: this.Id,
                            })
                            this.getDetail(this.Id)
                        })
                    })

                    break;
                case 'complete':
                    this.$confirm('工单完成后将不能再修改，请确保工单信息正确无误，再点击完成', '需求完成', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        formRequest(`/system/workOrder/updateStatus`, {
                            workOrderId: this.Id,
                            dynamicType: 'ORDER_STATUS_YIWANCHENG'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('操作成功');
                            formRequest('/system/workOrder/dynamic/publish', {
                                operatorId: sessionStorage.getItem('userId'),
                                dynamicContent: '更新工单状态从[验收通过]到[需求完成]',
                                workOrderMainId: this.Id,
                            })
                            this.getDetail(this.Id)
                        })
                    })

                    break;
                case 'editDraft':
                    this.editDraft()
                    break;
                case 'edit':

                    this.editType = true
                    break;
                case 'cencle':
                    this.$confirm('是否确认取消此工单？', '取消需求', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        formRequest(`/system/workOrder/updateStatus`, {
                            workOrderId: this.Id,
                            dynamicType: 'ORDER_STATUS_YIQUXIAO'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('操作成功');
                            formRequest('/system/workOrder/dynamic/publish', {
                                operatorId: sessionStorage.getItem('userId'),
                                dynamicContent: `更新工单状态从[${this.orderStatus(this.form.workOrderStatus)}]到[取消工单]`,
                                workOrderMainId: this.Id,
                            })
                            this.getDetail(this.Id)
                        })
                    })

                    break;
                case 'stop':
                    this.$confirm('是否确认暂停此工单？暂停后工单将变为已暂停状态，在恢复暂停前不能编辑', '暂停需求', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        formRequest(`/system/workOrder/updateStatus`, {
                            workOrderId: this.Id,
                            dynamicType: 'ORDER_STATUS_YIZHANTING'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('操作成功');
                            formRequest('/system/workOrder/dynamic/publish', {
                                operatorId: sessionStorage.getItem('userId'),
                                dynamicContent: `更新工单状态从[${this.orderStatus(this.form.workOrderStatus)}]到[暂停需求]`,
                                workOrderMainId: this.Id,
                            })
                            this.getDetail(this.Id)
                        })
                    })

                    break;
                case 'restore':
                    this.$confirm('是否确认恢复此暂停的工单?恢复后工单将变为待受理状态', '恢复暂停', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        formRequest(`/system/workOrder/updateStatus`, {
                            workOrderId: this.Id,
                            dynamicType: 'ORDER_STATUS_DAISHOULI'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('操作成功');
                            formRequest('/system/workOrder/dynamic/publish', {
                                operatorId: sessionStorage.getItem('userId'),
                                dynamicContent: `更新工单状态从[暂停需求]到[待受理]`,
                                workOrderMainId: this.Id,
                            })
                            this.getDetail(this.Id)
                        })
                    })

                    break;
                case 'delete':

                    break;

            }
        },
        orderStatus(e) {
            if (e == 'ORDER_STATUS_CAOGAO') {
                return '草稿'
            } else if (e == 'ORDER_STATUS_DAISHOULI') {
                return '待受理'
            }
            else if (e == 'ORDER_STATUS_YISHOULI') {
                return '已受理'
            } else if (e == 'ORDER_STATUS_DAIYANSHOU') {
                return '待验收'
            }
            else if (e == 'ORDER_STATUS_YANSHOUTONGGUO') {
                return '验收通过'
            } else if (e == 'ORDER_STATUS_YIWANCHENG') {
                return '需求完成'
            }
            else if (e == 'ORDER_STATUS_YIZHANTING') {
                return '已暂停'
            }
            else if (e == 'ORDER_STATUS_YIQUXIAO') {
                return '已取消'
            }
        },
        editDraft(row) {
            console.log('编辑草稿', row)

            this.addType = true


        },
        handleAccept(row) {
            console.log('受理', row)
            //如果row.personnelList中的对象departmentId和personnelId都为空，那么删除这个对象
            row.personnelList = row.personnelList.filter(item => item.departmentId || item.personnelId)
            let data = {
                files: row.fileList,
                departmentPersonnelListJson: JSON.stringify(row.personnelList),
            }


            formRequest('/system/workOrder/update', {
                workOrderTitle: row.workOrderTitle,
                workOrderType: row.workOrderType,
                requesterId: row.requesterId,
                requesterDepartmentId: row.requesterDepartmentId,
                requirementBackground: row.requirementBackground,
                requirementPurpose: row.requirementPurpose,
                requirementDescription: row.requirementDescription,
                expectedCompletionDate: row.expectedCompletionDate,
                requirementPriority: row.requirementPriority,
                requirementRemark: row.requirementRemark,
                fileIds: row.fileList && row.fileList.length > 0 ? row.fileList.map(item => { return item.id }) : [],
                workOrderStatus: 'ORDER_STATUS_YISHOULI',
                id: row.id,
                requesterId: row.requesterId,
                dynamicType: 'ORDER_DONGTAI_SHOULIGONGDAN',
                ...data
            }).then(res => {
                console.log(res);
                this.$message.success('受理成功')
                this.getDetail(res.data)

            })
        },
        saveDraft() {
            // 保存草稿逻辑
            this.$refs.form.validate((valid) => {
                if (valid) {

                }
            });
        },
        handleClose() {
            this.$emit('close');
        },
        handleChange(file, fileList) {

            this.fileList.push(file.raw)
            console.log(this.fileList);
        },
        handleRemove(file, fileList) {

            console.log(file);

            var findex = this.fileList
                .map((f) => f.uid)
                .indexOf(file.uid);
            if (findex > -1) {
                this.fileList.splice(findex, 1);
                if (this.form.fileIds) {
                    this.form.fileIds.splice(findex, 1);
                }
            }
        },
        beforeRemove(file, upFileList) {

        },
        beforeUpload(file, fileList) {


            //定义文件最大的限制，单位：MB
            var maxSize = 2048;
            //文件的大小
            var fileSize = file.size / 1024 / 1024;
            //进行文件的判断
            if (fileSize <= 0) {
                this.$message.error("上传文件大小不能为 0 MB");
                return false;
            } else if (fileSize < maxSize) {
                let promise = new Promise((resolve) => {
                    this.$nextTick(function () {
                        resolve(true);
                    });
                });
                return promise;
            } else {
                this.$message.error(`上传文件大小不能超过2G!`);
                return false;
            }
        },
        // 文件上传中处理
        handleFileUploadProgress(event, file, fileList) { },
        handleFileSuccess(response, file, fileList) {
            this.fileList = fileList;
            console.log(this.fileList);

            console.log(this.fileList);
        },
        handlePreview(file) {
            console.log(file, "===");

            if (file.name.endsWith(".pdf")) {
                //文件是pdf格式
                getFilesPathMapping().then((resp) => {
                    this.pdfUrl = resp.msg + (file.url || file.fileUrl);
                    window.open(this.pdfUrl);
                    return;
                });
                return;
            } else if (
                file.name.endsWith(".jpg") ||
                file.name.endsWith(".jpeg") ||
                file.name.endsWith(".png") ||
                file.name.endsWith(".gif")
            ) {
                //文件是图片格式
                getFilesPathMapping().then((resp) => {
                    this.photoUrl = resp.msg + (file.url || file.fileUrl);
                    console.log(this.photoUrl);
                    let array = new Set([]);
                    array.add(resp.msg + (file.url || file.fileUrl));
                    let from = Array.from(array);
                    this.imagePreviewUrls = from;
                    this.$refs.previewImg.showViewer = true;
                });
                // this.showImgViewer = true;
            } else {
                //文件下载
                this.handleDownload(file);
            }
        },

        handleDownload(file) {
            const url = file.url || file.fileUrl; //图片的https链接
            console.log(url);
            downloadByUrl({
                url: url,
            }).then((res) => {
                let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
                const link = document.createElement("a"); //创建一个隐藏的a标签
                link.target = "_blank";
                link.href = href; //设置下载的url
                link.download = file.name; //设置下载的文件名
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(href); // 释放掉blob对象
            });
        },
    }
};
</script>

<style scoped lang="less">
/deep/ .el-timeline {
    padding-left: 0 !important;
    margin-top: 16px;
}

/deep/ .el-timeline-item__tail {
    border-left: 2px solid #1890ff;
}

/deep/ .el-timeline-item__node {
    background-color: #fff;
    border: 2px solid #1890ff;
}

/deep/ .el-divider--horizontal {
    margin: 0 !important;
}

/deep/ .el-form-item {
    margin-bottom: 0 !important;
}

/deep/ .el-input,
.el-select {
    width: 220px !important;
}

.el-form-item {
    margin-bottom: 20px;
}

.dialog-footer {
    text-align: center;
    padding: 20px 0 0;
}
</style>
