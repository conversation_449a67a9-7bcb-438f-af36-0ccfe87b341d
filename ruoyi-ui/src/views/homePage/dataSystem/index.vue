<template>

  <div style="width: 100%;height:100%; " id="dataSystem">
          <div style="width: 100%;height:20px;"></div>
          <div style="width: 100%;height:35px; ">
                <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
                      <el-form-item label="系统" prop="platformNo">
                                <el-select v-model="platformNoParam" placeholder="请选择系统名称" filterable multiple size="small"
                                          >
                                  <el-option
                                    v-for="dict in platformNoSelect"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                      </el-form-item>
                      <el-form-item label="担保公司" prop="custNo">
                                <el-select v-model="custNoParam" placeholder="请选择担保公司" filterable multiple size="small"
                                          >
                                  <el-option
                                    v-for="dict in custNoSelect"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                      </el-form-item>
                      <el-form-item label="合作方" prop="partnerNo">
                              <el-select v-model="partnerNoParam" placeholder="请选择合作方" filterable multiple size="small"
                                        >
                                <el-option
                                  v-for="dict in partnerNoSelect"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                      </el-form-item>
                    <el-form-item label="资金方" prop="fundNo">
                            <el-select v-model="fundNoParam" placeholder="请选择资金方" filterable multiple size="small"
                                      >
                              <el-option
                                v-for="dict in fundNoSelect"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                              />
                            </el-select>
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">统计</el-button>
                      <!-- <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button> -->
                    </el-form-item>
                </el-form>
            </div>
        <!-- </el-col>
    </el-row> -->
    <div style="width: 100%;height:1px;">
          <el-divider></el-divider>
    </div>

    <!-- <el-row type="flex" :gutter="20">
      <el-col :span="1"> </el-col>
      <el-col :span="23"> </el-col>
    </el-row> -->
    <div style="width: 100%;height:10px;background-color:#FBFBFB;"></div>


    <div style="display: flex;width: 100%;background-color:#FFFFFF;flex-wrap: wrap;flex-direction: row;">
        <div class="innerone">
              <div style=" width: 80%; height:90px;  line-height: 40px;float:left">
                      <span class="amounting">在贷余额 <el-tooltip class="item" effect="dark" content="统计口径：资方口径。若合作方未在D+1日上送数据，会导致统计结果出现误差" placement="top">
                      <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                    </span>
                    <el-tooltip style="margin-left:24px" class="item" effect="dark" :content = "this.balanceAmount.toString()" placement="top">
                        <span class="span">
                          {{formaterMoney(this.balanceAmount)}} </span>
                      </el-tooltip>
                      <span style="font-size:16xp;color:#9D9D9D">万元</span>
              </div>
        </div>
        <div class="innerone" >
                <!-- <div style="width: 40%;height:90px;float:left " > -->
                  <div style="width: 100%;height:15px;"></div>
                  <div style="width: 100%;height:35px;height:35px;line-height:35px;">
                    <span class="grid-content">合作方  </span><span class="grid-contentfont"> {{this.partnerNum}}</span>
                    </div>
                  <div style="width: 100%;height:35px;height:35px;line-height:30px;"><span class="grid-content">资金方  </span> <span class="grid-contentfont"> {{this.fundNum}}</span></div>
                <!-- </div> -->
        </div>
        <div class="innerone">

        </div>
        <div class="innerone" >
          <!-- <div style="width: 100%;height:16px; background-color:#ccc;"></div> -->
              <el-button type="primary" icon = "el-icon-menu" style="margin-top:16px;margin-right:16px; float:right ;" @click="dialogVisible = true" size="mini">自定义布局</el-button>
        </div>
    </div>
    <div style="width: 100%;height:6px;background-color:#FBFBFB;"></div>
    <div style="display: flex;width: 100%;background-color:#FBFBFB;flex-wrap: wrap;flex-direction: row;">
       <div class="inneraaa">
          <div class="divsecend" style="line-height: 40px;padding-top:12px;"> <span  class="spancol2">累计贷款金额 <el-tooltip class="item" effect="dark" content="统计口径：资方口径。 若合作方未在D+1日上送数据，会导致统计结果出现误差" placement="top">
            <i class="el-icon-warning-outline"></i>
            </el-tooltip></span>
             <el-tooltip style="margin-left:24px" class="item" effect="dark" :content = "this.totalAmount.toString()" placement="top">
                 <span class="spancol">{{formaterMoney(this.totalAmount)}}</span>
             </el-tooltip>
            <span style="font-size:16xp;color:#666666;margin-left:5px">万元</span>
            </div>
       </div>
        <div class="inneraaa">
            <div class="divsecend" style="line-height: 40px;padding-top:12px;">
              <span class="spancol2">累计贷款笔数 <el-tooltip class="item" effect="dark" content="统计口径：资方口径。 若合作方未在D+1日上送数据，会导致统计结果出现误差" placement="top">
              <i class="el-icon-warning-outline"></i>
              </el-tooltip></span>
                 <el-tooltip style="margin-left:24px" class="item" effect="dark" :content = "this.totalCount.toString()" placement="top">
                     <span class="spancol">{{formaterMoney(this.totalCount)}}</span>
                 </el-tooltip>
              <span style="font-size:16xp;color:#666666;margin-left:5px">万</span>
            </div>
       </div>
        <div class="inneraaa">
           <div class="divsecend" style="line-height: 40px;padding-top:12px;">
                <span class="spancol2">昨日新增贷款本金 <el-tooltip class="item" effect="dark" content="统计口径：资方口径。 若合作方未在D+1日上送数据，会导致统计结果出现误差" placement="top">
                <i class="el-icon-warning-outline"></i>
                </el-tooltip></span>
                <el-tooltip style="margin-left:24px" class="item" effect="dark" :content = "this.addTotalAmount.toString()" placement="top">
                    <span class="spancol">{{formaterMoney(this.addTotalAmount)}}</span>
                </el-tooltip>
                <span style="font-size:16xp;color:#666666;margin-left:5px">万元</span>
            </div>
       </div>
        <div class="inneraaa">
          <div class="divsecend" style="line-height: 40px;padding-top:12px;">
              <span class="spancol2">昨日新增贷款笔数 <el-tooltip class="item" effect="dark" content="统计口径：资方口径。 若合作方未在D+1日上送数据，会导致统计结果出现误差" placement="top">
              <i class="el-icon-warning-outline"></i>
              </el-tooltip> </span>
                  <span class="spancol">{{this.addTotalCount}}</span>

            </div>
       </div>
    </div>

    <div style="display: flex;width: 100%;background-color:#FBFBFB;flex-wrap: wrap;flex-direction: row;">

        <div v-loading="loading" class="inner" v-show="balance">

            <div style="width: 100%;height:50px;">
              <div style="width: 100%;height:20px;"></div>
               <span class="spanfont1">{{balanceFont}}</span>
               <el-button-group style="margin-right:30px;float:right">
                  <el-button @click="balanceAmountDay()">日</el-button>
                  <el-button @click="balanceAmountWeek()">周</el-button>
                  <el-button @click="balanceAmountMonth()">月</el-button>
                  <el-button @click="balanceAmountYear()">年</el-button>
                </el-button-group>
                  <div style="width: 100%;height:1px;float:right">
                   <el-divider></el-divider>
                  </div>
            </div>
          <div  id="charts1" style="width: 100%;height:400px;"></div>
            <div style="width: 100%;height:50px;" >
              <el-button @click="spikDdata" style="margin-left:45px;" size="mini" class="echartspan">
                更多<i class="el-icon-arrow-right el-icon--right"></i>
              </el-button>

            </div>
        </div>

        <div  v-loading="loading" class="inner" v-show="repayCount">
            <div style="width: 100%;height:30px;">
                <div style="width: 100%;height:20px;"></div>
                <span class="spanfont1">{{repayFont}}</span>
                <el-button-group style="margin-right:30px;float:right">
                  <el-button @click="repayCountDay()">日</el-button>
                  <el-button @click="repayCountWeek()">周</el-button>
                  <el-button @click="repayCountMonth()">月</el-button>
                  <el-button @click="repayCountYear()">年</el-button>
                </el-button-group>
                    <div style="width: 100%;height:1px;float:right">
                      <el-divider></el-divider>
                    </div>
              </div>
              <div id="repayCountEchartDiv" style="width: 100%;height:450px; float:left"></div>
        </div>

        <div  v-loading="loading" class="inner" v-show="addAmounton">
            <div style="width: 100%;height:30px;">
                <div style="width: 100%;height:20px;"></div>

                <span class="spanfont1">{{addCountFont}}</span>
                <el-button-group style="margin-right:30px;float:right">
                   <el-button @click="addCountDay()">日</el-button>
                  <el-button @click="addCountWeek()">周</el-button>
                  <el-button @click="addCountMonth()">月</el-button>
                  <el-button @click="addCountYear()">年</el-button>
                </el-button-group>
                    <div style="width: 100%;height:1px;float:right">
                      <el-divider></el-divider>
                    </div>
              </div>
              <div id="charts2" style="width: 100%;height:450px; float:left"></div>
        </div>



        <div v-loading="loading" class="inner" v-show="balancePartnerStack">
          <div style="width: 100%;height:30px;">
                <div style="width: 100%;height:20px;"></div>
                <span class="spanfont1">{{ balancePartnerStackname }}</span>
            <el-button-group style="margin-right:30px;float:right">
              <el-button @click="zdyehzfDay()">日</el-button>
              <el-button @click="zdyehzfWeek()">周</el-button>
              <el-button @click="zdyehzfMonth()">月</el-button>
              <el-button @click="zdyehzfYear()">年</el-button>
            </el-button-group>
                <el-button  style="float: right;"    @click="chakandt()">查看大图</el-button>
                    <div style="width: 100%;height:1px;float:right">
                      <el-divider></el-divider>
                    </div>
            </div>
            <div  id="parStack" style="width: 100%;height:400px; float:left"></div>
            <div style="width: 100%;height:50px; float:left" >
                <el-button @click="spikpartner" style="margin-left:45px;" size="mini" class="echartspan">
                更多<i class="el-icon-arrow-right el-icon--right"></i>
              </el-button>

            </div>
        </div>
      <el-dialog
        title=""
        :visible.sync="chakandatu"  width="80%" >
          <div style="width: 100%;height:60px;">
            <div style="width: 100%;height:20px;"></div>
            <span class="spanfont">{{ balancePartnerStackname }}</span>
            <el-button  style="float: right;"    @click="chakandt()">点击刷新</el-button>
            <div style="width: 100%;height:1px;float:right">
            </div>
          </div>

          <div v-if="chakandatu" id="parStackas" style="width: 100%;height:600px;"></div>

          <div style="width: 100%;height:50px; float:left" >
            <el-button @click="spikpartner" style="margin-left:45px;" size="mini" class="echartspan">
              更多<i class="el-icon-arrow-right el-icon--right"></i>
            </el-button>

          </div>

          <div slot="footer" class="dialog-footer">
            <el-button style="display:block;margin:0 auto" @click="chakandtg">关闭</el-button>
          </div>
      </el-dialog>
        <div v-loading="loading" class="inner" v-show="balanceFundStack">
          <div style="width: 100%;height:30px;">
                <div style="width: 100%;height:20px;"></div>
                <span class="spanfont1">{{balancePartnerStacknamezjf}}</span>
            <el-button-group style="margin-right:30px;float:right">
              <el-button @click="zdyzjfDay()">日</el-button>
              <el-button @click="zdyezjfWeek()">周</el-button>
              <el-button @click="zdyezjfMonth()">月</el-button>
              <el-button @click="zdyezjfYear()">年</el-button>
            </el-button-group>
            <el-button  style="float: right;"    @click="chakandtzjf()">查看大图</el-button>
                    <div style="width: 100%;height:1px;float:right">
                      <el-divider></el-divider>
                    </div>
            </div>
            <div  id="fundStack" style="width: 100%;height:400px; float:left"></div>
            <div style="width: 100%;height:50px; float:left" >
                <el-button @click="spikfund" style="margin-left:45px;" size="mini" class="echartspan">
                更多<i class="el-icon-arrow-right el-icon--right"></i>
              </el-button>

            </div>
        </div>
      <el-dialog
        title=""
        :visible.sync="chakandatuzjf"  width="80%" >
        <div style="width: 100%;height:60px;">
          <div style="width: 100%;height:20px;"></div>
          <span class="spanfont">{{balancePartnerStacknamezjf}}</span>
          <el-button  style="float: right;"    @click="chakandtzjf()">点击刷新</el-button>
          <div style="width: 100%;height:10px;float:right">
          </div>
        </div>

        <div v-if="chakandatuzjf" id="fundStackas" style="width: 100%;height:800px; "></div>
        <div style="width: 100%;height:50px; float:left" >
          <el-button @click="spikfund" style="margin-left:45px;" size="mini" class="echartspan">
            更多<i class="el-icon-arrow-right el-icon--right"></i>
          </el-button>

        </div>

        <div slot="footer" class="dialog-footer">
          <el-button style="display:block;margin:0 auto" @click="chakandtg">关闭</el-button>
        </div>
      </el-dialog>


        <div v-loading="loading" class="inner" v-show="partnerpieon">
          <div style="width: 100%;height:30px;">
                <div style="width: 100%;height:20px;"></div>
                <span class="spanfont1">各合作方占比</span>
                    <div style="width: 100%;height:1px;float:right">
                      <el-divider></el-divider>
                    </div>
            </div>
            <div  id="echartParpie" style="width: 100%;height:400px; float:left"></div>
            <div style="width: 100%;height:50px; float:left" >
                <!-- <a class="echartspan" @click="spikpartner">&nbsp;更多 ></a> -->
                <!-- <el-button @click="spikpartner" style="margin-left:45px;" size="mini"><span class="echartspan">更多 ></span> -->
                    <!-- </el-button> -->
                <el-button @click="spikpartner" style="margin-left:45px;" size="mini" class="echartspan">
                更多<i class="el-icon-arrow-right el-icon--right"></i>
              </el-button>

            </div>
        </div>
        <div  v-loading="loading" class="inner" v-show="partnerbar">
          <div style="width: 100%;height:30px;">
                <div style="width: 100%;height:20px;"></div>
                <span class="spanfont1">各合作方在贷余额分布</span>
                    <div style="width: 100%;height:1px;float:right">
                      <el-divider></el-divider>
                    </div>
              </div>
              <div id="echartParBar" style="width: 100%;height:450px; float:left"></div>
        </div>

        <div v-loading="loading" class="inner" v-show="fundpieon">
          <div style="width: 100%;height:30px;">
                <div style="width: 100%;height:20px;"></div>
                <span class="spanfont1">各资金方占比</span>
                    <div style="width: 100%;height:1px;float:right">
                      <el-divider></el-divider>
                    </div>
              </div>
               <div  id="echartfundpie" style="width: 100%;height:400px; float:left"></div>
              <div style="width: 100%;height:50px; float:left" >
                <!-- <a class="echartspan" @click="spikfund">&nbsp;更多 ></a> -->
                <!-- <el-button @click="spikfund" style="margin-left:45px;" size="mini"><span class="echartspan">更多 ></span></el-button> -->
                  <el-button @click="spikfund" style="margin-left:45px;" size="mini" class="echartspan">
                更多<i class="el-icon-arrow-right el-icon--right"></i>
              </el-button>
              </div>

        </div>
        <div v-loading="loading" class="inner" v-show="fundbar">

           <div style="width: 100%;height:30px;">
                <div style="width: 100%;height:20px;"></div>
                <span class="spanfont1">各资金方在贷余额分布</span>
                    <div style="width: 100%;height:1px;float:right">
                      <el-divider></el-divider>
                    </div>
              </div>
               <div  id="echartfundBar" style="width: 100%;height:450px; float:left"></div>
        </div>

       <div v-loading="loading" class="inner" v-show="custpieon">
          <div style="width: 100%;height:30px;">
                <div style="width: 100%;height:20px;"></div>
                <span class="spanfont1">各担保公司占比</span>
                    <div style="width: 100%;height:1px;float:right">
                      <el-divider></el-divider>
                    </div>
              </div>
               <div  id="echartCustpie" style="width: 100%;height:400px; float:left"></div>
              <div style="width: 100%;height:50px; float:left" >
                <!-- <el-button @click="spikcust" style="margin-left:45px;" size="mini"><span class="echartspan">更多 ></span></el-button> -->
                  <el-button @click="spikcust" style="margin-left:45px;" size="mini" class="echartspan">
                更多<i class="el-icon-arrow-right el-icon--right"></i>
              </el-button>
              </div>

        </div>
        <div v-loading="loading" class="inner" v-show="custbar">

           <div style="width: 100%;height:30px;">
                <div style="width: 100%;height:20px;"></div>
                <span class="spanfont1">各担保公司在贷余额分布</span>
                    <div style="width: 100%;height:1px;float:right">
                      <el-divider></el-divider>
                    </div>
              </div>
               <div  id="echartCustBar" style="width: 100%;height:450px; float:left"></div>
        </div>


        <div v-loading="loading" class="inner" v-show="vintageon">
            <div style="width: 100%;height:30px;">
                <div style="width: 100%;height:20px;"></div>
                <span class="spanfont1">Vintage（30+）</span>
                    <div style="width: 100%;height:1px;float:right">
                      <el-divider></el-divider>
                    </div>
              </div>

              <div  id="charts3"  style="width: 100%;height:400px; float:left"></div>
              <div style="width: 100%;height:50px; float:left" >
                <!-- <el-button @click="spikVintage" style="margin-left:45px;" size="mini"><span class="echartspan">更多 ></span></el-button> -->
                <!-- <a class="echartspan" @click="spikVintage">&nbsp;更多 ></a> -->
                    <el-button @click="spikVintage" style="margin-left:45px;" size="mini" class="echartspan">
                更多<i class="el-icon-arrow-right el-icon--right"></i>
              </el-button>
              </div>


        </div>
<!--        <div v-loading="loading" class="inner" v-show="profiton">-->
<!--          <div style="width: 100%;height:30px;">-->
<!--                <div style="width: 100%;height:20px;"></div>-->
<!--                <span class="spanfont1">利润测算（近12个月）</span>-->
<!--                    <div style="width: 100%;height:1px;float:right">-->
<!--                      <el-divider></el-divider>-->
<!--                    </div>-->
<!--              </div>-->
<!--              <div  id="charts4"  style="width: 100%;height:400px; float:right"></div>-->
<!--            <div style="width: 50%;height:50px; float:left" >-->
<!--              &lt;!&ndash; <el-button @click="spikProfit" style="margin-left:45px;" size="mini"><span class="echartspan">更多 ></span></el-button> &ndash;&gt;-->
<!--              <el-button @click="spikProfit" style="margin-left:45px;" size="mini" class="echartspan">-->
<!--                更多<i class="el-icon-arrow-right el-icon&#45;&#45;right"></i>-->
<!--              </el-button>-->
<!--            </div>-->
<!--        </div>-->

      <!-- <div v-loading="loading" class="inner" v-show="profiton">
        <div style="width: 100%;height:30px;">
          <div style="width: 100%;height:20px;"></div>
          <span class="spanfont1">产品利润堆叠（近12个月）</span>
          <div style="width: 100%;height:1px;float:right">
            <el-divider></el-divider>
          </div>
        </div>
        <div  id="profitRank"  style="width: 100%;height:400px; float:right"></div>
        <div style="width: 50%;height:50px; float:left" >
          <el-button @click="spikProfitRank" style="margin-left:45px;" size="mini" class="echartspan">
            更多<i class="el-icon-arrow-right el-icon--right"></i>
          </el-button>
        </div>
      </div> -->

      <!-- <div v-loading="loading" class="inner" v-show="badDebtDiv">
          <div style="width: 100%;height:30px;">
                <div style="width: 100%;height:20px;"></div>
                <span class="spanfont1">年化坏账率</span>
                    <div style="width: 100%;height:1px;float:right">
                      <el-divider></el-divider>
                    </div>
              </div>
              <div  id="chartsBadDebt"  style="width: 100%;height:400px; float:right"></div>
            <div style="width: 50%;height:50px; float:left" >
              <el-button @click="spikbadDebt" style="margin-left:45px;" size="mini" class="echartspan">
                更多<i class="el-icon-arrow-right el-icon--right"></i>
              </el-button>
            </div>
        </div> -->

      </div>

      <el-dialog
      title="自定义布局"
        :visible.sync="dialogVisible"
        width="25%"
        :before-close="handleClose">
        <div style="width: 100%;height:1px;float:left">
                    <el-divider></el-divider>
        </div>
        <div style="width: 100%;float:left background-color:#FBFBFB;">
          <el-row></el-row>
          <el-row></el-row>
          <el-row><span class="dialogspan">请选择在首页展示的图表</span></el-row>
          <el-row><el-checkbox v-model="balance">在贷余额（近90天）</el-checkbox></el-row>
          <el-row><el-checkbox v-model="repayCount">每日放还款金额（近30天）</el-checkbox></el-row>

          <el-row><el-checkbox v-model="addAmounton">每日新增贷款笔数（近90天）</el-checkbox></el-row>

          <el-row><el-checkbox v-model="balancePartnerStack">在贷余额-合作方堆叠（近90天）</el-checkbox></el-row>
          <el-row><el-checkbox v-model="balanceFundStack">在贷余额-资金方堆叠（近90天）</el-checkbox></el-row>

          <el-row><el-checkbox v-model="partnerpieon">各合作方占比</el-checkbox></el-row>
          <el-row><el-checkbox v-model="partnerbar">各合作方在贷余额分布</el-checkbox></el-row>
          <el-row><el-checkbox v-model="fundpieon">各资金方占比</el-checkbox></el-row>
          <el-row><el-checkbox v-model="fundbar">各资金方在贷余额分布</el-checkbox></el-row>

          <el-row><el-checkbox v-model="custpieon">各担保公司占比</el-checkbox></el-row>
          <el-row><el-checkbox v-model="custbar">各担保公司在贷余额分布</el-checkbox></el-row>

          <el-row><el-checkbox v-model="vintageon">Vintage（30+）</el-checkbox></el-row>
          <el-row><el-checkbox v-model="profiton">利润测算（近12个月）</el-checkbox></el-row>
<!--          <el-row><el-checkbox v-model="profiton">产品利润堆叠（近12个月）</el-checkbox></el-row>
          <el-row><el-checkbox v-model="badDebtDiv">年化坏账率</el-checkbox></el-row>-->

        </div>



        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="updatediyLount()">确 定</el-button>
        </div>

      </el-dialog>


  </div>

</template>
<script>
import * as echarts from 'echarts';
import { updateloginLayout,loginLayout,homeChartData} from '@/api/system/view'
//  start 引用联级字典查询
import {getSysDictRefList,getSelectSysDictRefList} from '@/api/ref/ref'
//  end 引用联级字典查询
export default {
   name: 'DataSystem',
  data() {
    return {

         chakandatu:false,
      chakandatuzjf:false,
      //堆叠的数据
      profitRankStack:[],

      //堆叠的数据
      fundStackData:[],
      //堆叠的x轴
      fundStackXaxis:[],

      //堆叠的数据
      parStackData:[],
      //堆叠的x轴
      parStackXaxis:[],
      //合作方
      dayRepayAmountData:[],
      dayRepayAmountXaxis:[],
      weekRepayAmountData:[],
      weekRepayAmountXaxis:[],
      monthRepayAmountData:[],
      monthRepayAmountXaxis:[],
      yearRepayAmountData:[],
      yearRepayAmountXaxis:[],
      //资金方
      dayfundAmountData:[],
      dayfundAmountXaxis:[],
      weekfundAmountData:[],
      weekfundAmountXaxis:[],
      monthfundAmountData:[],
      monthfundAmountXaxis:[],
      yearfundAmountData:[],
      yearfundAmountXaxis:[],
      //在贷余额标题
      balanceFont:"",
      //新增贷款笔数标题
      addCountFont:"",
      //每日放还款标题
      repayFont:"",
      //坏账率月份
      // badDebtMonth:'',
      showgreap:true,

      dialogVisible:false,
      //控制是否展示
      repayCount:'',
      balance:'',
      addAmounton:'',
      partnerpieon:'',
      partnerbar:'',
      fundpieon:'',
      fundbar:'',
      custpieon:'',
      custbar:'',
      vintageon:'',
      profiton:'',
      badDebtDiv:'',
      balancePartnerStack:'',
      balancePartnerStackname:'',
      balancePartnerStacknamezjf:'',
      balanceFundStack:'',
      //坏账率echart数据
      badDebtEchartData:[],
      badDebtxixas:[],
      //合作方echart数据
      partnerpieData:[],
      partnerxixas:[],
      partnerbarData:[],
      //资产方echart数据
      fundpieData:[],
      fundxixas:[],
      fundbarData:[],
      //担保公司echart数据
      custpieData:[],
      custxixas:[],
      custbarData:[],
      //每日放还款金额echart数据
       repayfangamount :[],
      repayhuanamount :[],
      repayCountXixas:[],
      //在贷余额和新增贷款笔数日周月年x轴数据
      dayxaxisData:[],
      weekxaxisData:[],
      monthxaxisData:[],
      yearxaxisData:[],
      //在贷余额日周月年数据
      dayAmountData:[],
      weekAmountData:[],
      monthAmountData:[],
      yearAmountData:[],
      //新增贷款笔数日周月年数据
      dayAddCountData:[],
      weekAddCountData:[],
      monthAddCountData:[],
      yearAddCountData:[],
      //放还款统计x轴
      dayRepayCountxaxis:[],
      weekRepayCountxaxis:[],
      monthRepayCountxaxis:[],
      yearRepayCountxaxis:[],
      //放还款数据
      dayfangData:[],
      dayhuanData:[],
      weekfangData:[],
      weekhuanData:[],
      monthfangData:[],
      monthhuanData:[],
      yearfangData:[],
      yearhuanData:[],
      //在贷余额周月年x轴
      weekendxaxis:[],
      monthendxaxis:[],
      yearendxaxis:[],

      partnerNum:"",
      fundNum:"",
      totalAmount:"",
      totalCount:"",
      addTotalAmount:"",
      addTotalCount:"",
      balanceAmount:"",
      echartBalanceAmount:"",
      echarTotalCount:"",
      amountXixas:"",
      addcountXixas:"",
      profitXaxis:[],
      profitEchartData:[],

       legendDatas:[],
      stackbarXaxis:[],
      stackbarData:[],
      loginUserRole:"",
      //  start 新增参数
      platformNoParam: '',
      custNoParam: '',
      partnerNoParam: '',
      fundNoParam: '',
      productNoParam: '',


      sysDictRefParam: {
        dictType: '',
        dictValue: '',
        pDictType: '',
        pDictValue: '',
        selectDictDatas:''
      },
      platformNoSelect: [],
      custNoSelect: [],
      fundNoSelect: [],
      partnerNoSelect: [],
      productNoSelect: [],
      // end 新增参数

      //外部系统
      externalsystems:[],
      //担保公司编码
      dbcompany:[],
      //合作方
      partnerdata:[],
      //资金方
      capitaldata:[],
      querydatatype:"cust_no",
      externalsystem:"platform_no",
      partnerscode:"partner_no",
      capitalcode:"fund_no",
      productcode:"product_no",

        // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
       // 表单参数
      form: {},
       // 查询参数
      queryParams: {
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        params:{
           moduleTypeOfNewAuth: 'DATATOP'
        }
      },
    };
  }, mounted(){

    this.getList();
     this.getLoginLayout();
     this.initSelectData();

  },
  methods:{
    chakandt(){
      this.chakandatu = true;
      this.$nextTick(()=>{
        this.parStackEChartas();
      })


    }, chakandtzjf(){
      this.chakandatuzjf = true;
      this.$nextTick(()=>{
        this.fundStackEChartas();
      })


    },
    chakandtg(){
      this.chakandatu = false;
      this.chakandatuzjf = false;
    },
    //保存自定义布局
    updatediyLount(){
      this.dialogVisible = false
      var updateData = {
        balance : this.balance,
        repayCount:this.repayCount,
        addAmount:this.addAmounton,
        partnerPie:this.partnerpieon,
        partnerBar:this.partnerbar,
        fundPie:this.fundpieon,
        fundBar:this.fundbar,
        custPie:this.custpieon,
        custBar:this.custbar,
        vintage: this.vintageon,
        profit:this.profiton,
        badDebt: this.badDebtDiv,
        balancePartnerStack:this.balancePartnerStack,
        balanceFundStack:this.balanceFundStack
      }
      updateloginLayout(updateData).then(response=>{

      })
    },
    //查询自定义布局
    getLoginLayout(){
        loginLayout().then(response=>{
             this.repayCount = response.repayCount
              this.balance = response.balance
              this.addAmounton = response.addAmounton
              this.partnerpieon = response.partnerpieon
              this.partnerbar = response.partnerbar
              this.fundpieon = response.fundpieon
              this.fundbar = response.fundbar
              this.custpieon = response.custpieon
              this.custbar = response.custbar
              this.vintageon = response.vintageon
              this.profiton = response.profiton
              this.badDebtDiv = response.badDebtDiv
              this.balancePartnerStack = response.balancePartnerStack
              this.balanceFundStack = response.balanceFundStack

        })
    },
//在贷余额
    zdyzjfDay(){

      this.fundStackData = this.dayfundAmountData
      this.fundStackXaxis =this.dayfundAmountXaxis

      this.balancePartnerStacknamezjf = "在贷余额-资金方（近90天大于10亿）";

      this.fundStackEChart();
    }, //在贷余额
    zdyezjfWeek(){
      this.fundStackData = this.weekfundAmountData
      this.fundStackXaxis =this.weekfundAmountXaxis

      this.balancePartnerStacknamezjf = "在贷余额-资金方（近30周大于10亿）";

      this.fundStackEChart();
    }, //在贷余额
    zdyezjfMonth(){
      this.fundStackData = this.monthfundAmountData
      this.fundStackXaxis =this.monthfundAmountXaxis
      this.balancePartnerStacknamezjf = "在贷余额-资金方（近24月大于10亿）";
      this.fundStackEChart();
    }, //在贷余额
    zdyezjfYear(){
      this.fundStackData = this.yearfundAmountData
      this.fundStackXaxis =this.yearfundAmountXaxis
      this.balancePartnerStacknamezjf = "在贷余额-资金方（近5年大于10亿）";
      this.fundStackEChart();
    },



    //在贷余额
    zdyehzfDay(){

      this.parStackData = this.dayRepayAmountData
      this.parStackXaxis =this.dayRepayAmountXaxis

        this.balancePartnerStackname = "在贷余额-合作方（近90天大于10亿）";

        this.parStackEChart();
    }, //在贷余额
    zdyehzfWeek(){
      this.parStackData = this.weekRepayAmountData
      this.parStackXaxis =this.weekRepayAmountXaxis

      this.balancePartnerStackname = "在贷余额-合作方（近30周大于10亿）";

        this.parStackEChart();
    }, //在贷余额
    zdyehzfMonth(){
      this.parStackData = this.monthRepayAmountData
      this.parStackXaxis =this.monthRepayAmountXaxis
        this.balancePartnerStackname = "在贷余额-合作方（近24月大于10亿）";
      this.parStackEChart();
    }, //在贷余额
    zdyehzfYear(){
      this.parStackData = this.yearRepayAmountData
      this.parStackXaxis =this.yearRepayAmountXaxis
        this.balancePartnerStackname = "在贷余额-合作方（近5年大于10亿）";
      this.parStackEChart();
    },



    //在贷余额日
    balanceAmountDay(){
        this.balanceFont = "在贷余额（近90天）";
        this.echartBalanceAmount = this.dayAmountData;


        this.amountXixas = this.dayxaxisData;
        this.balanceAmountEChart();
    },
    //在贷余额周
    balanceAmountWeek(){
      this.balanceFont = "在贷余额（近90周）";

       this.echartBalanceAmount = this.weekAmountData;
        this.amountXixas = this.weekendxaxis;
        this.balanceAmountEChart();
    },
    //在贷余额月
    balanceAmountMonth(){
      this.balanceFont = "在贷余额（近24个月）";
      this.echartBalanceAmount = this.monthAmountData;
        this.amountXixas = this.monthendxaxis;
        this.balanceAmountEChart();
    },
    //在贷余额年
    balanceAmountYear(){
      this.balanceFont = "在贷余额（近5年）";
      this.echartBalanceAmount = this.yearAmountData;
        this.amountXixas = this.yearendxaxis;
        this.balanceAmountEChart();
    },


    //新增贷款笔数日
      addCountDay(){
        this.addCountFont = "每日新增贷款笔数（近90天）";
        this.echarTotalCount = this.dayAddCountData;
        this.addcountXixas = this.dayxaxisData;
        this.totalCountEChart();
    },
    //新增贷款笔数周
    addCountWeek(){
      this.addCountFont = "每周新增贷款笔数（近90周）";
       this.echarTotalCount = this.weekAddCountData;
        this.addcountXixas = this.weekxaxisData;
        this.totalCountEChart();
    },
    //新增贷款笔数月
    addCountMonth(){
      this.addCountFont = "每月新增贷款笔数（近24个月）";
      this.echarTotalCount = this.monthAddCountData;
        this.addcountXixas = this.monthxaxisData;
        this.totalCountEChart();
    },
    //新增贷款笔数年
    addCountYear(){
      this.addCountFont = "每年新增贷款笔数（近5年）";
      this.echarTotalCount = this.yearAddCountData;
        this.addcountXixas = this.yearxaxisData;
        this.totalCountEChart();
    },

  //每日放还款
    repayCountDay(){
      this.repayFont = "每日放还款金额（近30天）";
      this.repayfangamount = this.dayfangData;
      this.repayhuanamount = this.dayhuanData;
      this.repayCountXixas = this.dayRepayCountxaxis;
      this.repayCountEchart();
    },
     //每周放还款
    repayCountWeek(){
      this.repayFont = "每周放还款金额（近30周）"
      this.repayfangamount = this.weekfangData;
      this.repayhuanamount = this.weekhuanData;
      this.repayCountXixas = this.weekRepayCountxaxis ;
      this.repayCountEchart();
    },
     //每月放还款
    repayCountMonth(){
        this.repayFont = "每月放还款金额（近24个月）"
      this.repayfangamount = this.monthfangData;
      this.repayhuanamount = this.monthhuanData;
      this.repayCountXixas = this.monthRepayCountxaxis;
      this.repayCountEchart();
    },
     //每年放还款
    repayCountYear(){
      this.repayFont = "每年放还款金额（近5年）"
      this.repayfangamount = this.yearfangData;
      this.repayhuanamount = this.yearhuanData;
      this.repayCountXixas = this.yearRepayCountxaxis;
      this.repayCountEchart();
    },
   /** 格式化金额 */
     formaterMoney(data) {

           if (!data) return '0.00'
      if(data==='-') return '-'
      var a = data/10000
      // 将数据分割，保留两位小数
       a = this.toFixedFun(a,2)
      // a= a.toFixed(2)
      // 获取整数部分
      const intPart = Math.trunc(a)

       // 整数部分处理，增加,
      const intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
      // 预定义小数部分
      let floatPart = '.00'
      // 将数据分割为小数部分和整数部分
      const newArr = a.toString().split('.')
      if (newArr.length === 2) { // 有小数部分
        floatPart = newArr[1].toString() // 取得小数部分
        if (1/intPart < 0 && intPart === 0) {
          return '-' + intPartFormat + '.' + floatPart
        }
        return intPartFormat + '.' + floatPart
      }
     if (1/intPart < 0 && intPart === 0) {
       return '-' + intPartFormat + '.' + floatPart
     }
      return intPartFormat + floatPart
        // return a
    },
//四舍五入
 toFixedFun (data, len){
  // debugger
  const number = Number(data);
  if (isNaN(number) || number >= Math.pow(10, 21)) {
    return number.toString();
  }
  if (typeof (len) === 'undefined' || len === 0) {
    return (Math.round(number)).toString();
  }
  let result = number.toString();
  const numberArr = result.split('.');

  if (numberArr.length < 2) {
    // 整数的情况
    return padNum(result);
  }
  const intNum = numberArr[0]; // 整数部分
  const deciNum = numberArr[1];// 小数部分
  const lastNum = deciNum.substr(len, 1);// 最后一个数字

  if (deciNum.length === len) {
    // 需要截取的长度等于当前长度
    return result;
  }
  if (deciNum.length < len) {
    // 需要截取的长度大于当前长度 1.3.toFixed(2)
    return padNum(result);
  }
  // 需要截取的长度小于当前长度，需要判断最后一位数字
  result = `${intNum}.${deciNum.substr(0, len)}`;
  if (parseInt(lastNum, 10) >= 5) {
    // 最后一位数字大于5，要进位
    const times = Math.pow(10, len); // 需要放大的倍数
    let changedInt = Number(result.replace('.', ''));// 截取后转为整数
    changedInt++; // 整数进位
    changedInt /= times;// 整数转为小数，注：有可能还是整数
    result = padNum(`${changedInt }`);
  }
  return result;
  // 对数字末尾加0
  function padNum(num) {
    const dotPos = num.indexOf('.');
    if (dotPos === -1) {
      // 整数的情况
      num += '.';
      for (let i = 0; i < len; i++) {
        num += '0';
      }
      return num;
    } else {
      // 小数的情况
      const need = len - (num.length - dotPos - 1);
      for (let j = 0; j < need; j++) {
        num += '0';
      }
      return num;
    }
    }},


    initSelectData() {
      getSelectSysDictRefList({ unitType: 4 , moduleTypeOfNewAuth: 'DATATOP'}).then((response) => {
        this.platformNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 0 , moduleTypeOfNewAuth: 'DATATOP'}).then((response) => {
        this.custNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 3 , moduleTypeOfNewAuth: 'DATATOP'}).then((response) => {
        this.productNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 2 , moduleTypeOfNewAuth: 'DATATOP'}).then((response) => {
        this.fundNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 1 , moduleTypeOfNewAuth: 'DATATOP'}).then((response) => {
        this.partnerNoSelect = response;
      });
    },
     getCustNoList(val) {
      const flag = this.lateByte(this.queryParams.platformNo) > this.lateByte(val.toString())
      this.queryParams.platformNo = this.platformNoParam.toString()
      if (val == null || val === '' || flag) {
        this.custNoSelect = null
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.custNo = null
        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.custNoParam = null
        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {

            // this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            // this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getPartnerNoList(val) {
      const flag = this.lateByte(this.queryParams.custNo) > this.lateByte(val.toString())
      this.queryParams.custNo = this.custNoParam.toString()
      if (val == null || val === '' || flag) {
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            // this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;

            // this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getFundNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())
      this.queryParams.partnerNo = this.partnerNoParam.toString()
      if (val == null || val === ''|| flag) {
        this.fundNoSelect = null
        this.productNoSelect = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
         this.sysDictRefParam.selectDictDatas =""

      if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            // this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
      else {
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
        this.sysDictRefParam.selectDictDatas =""
         if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            // this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getProductNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())

      this.queryParams.fundNo = this.fundNoParam.toString()
      if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo
this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            // this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo

this.sysDictRefParam.selectDictDatas =""
        if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            // this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
      getProductNoValue(val) {
      const flag = this.lateByte(this.queryParams.productNo) > this.lateByte(val.toString())

      this.queryParams.productNo = this.productNoParam.toString()

       if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        this.productNoParam = null
        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue =''
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            // this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue = this.queryParams.productNo

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            // this.productNoSelect= response.data.product_no
        })
      }

    },
     lateByte(sTargetStr) {
      var sTmpStr, sTmpChar;
      var nOriginLen = 0;
      var nStrLength = 0;

      sTmpStr = new String(sTargetStr);
      nOriginLen = sTmpStr.length;

      for (var i = 0; i < nOriginLen; i++) {
        sTmpChar = sTmpStr.charAt(i);

        if (escape(sTmpChar).length > 4) {
          nStrLength += 2;
        } else if (sTmpChar != '/r') {
          nStrLength++;
        }
      }
      return nStrLength;
    },
    //end




 /** 查询数据 */
     async getList() {
      this.loading = true;
      this.queryParams.platformNo = this.platformNoParam.toString();
      this.queryParams.custNo = this.custNoParam.toString();
      this.queryParams.partnerNo = this.partnerNoParam.toString();
      this.queryParams.fundNo = this.fundNoParam.toString();
      this.queryParams.productNo = this.productNoParam.toString();
       await homeChartData(this.queryParams).then(response => {
         this.partnerNum = response.partnerCount,
         this.fundNum = response.fundCount,
          this.totalAmount= response.totalAmount,
          this.totalCount=response.totalCount,
          this.addTotalAmount=response.addAmount,
          this.addTotalCount=response.addCount,
          this.balanceAmount = response.totalBalanceAmount,
          //坏账率首页统计图
          this.badDebtEchartData = response.badDebtdataList,
          this.badDebtxixas = response.badDebtxaxisData,
          this.badDebtMonth = response.badDebtMonth,
          //合作方echart数据
          this.partnerpieData=response.partnerPieData,
       this.partnerxixas=response.partnerXaxis,
       this.partnerbarData=response.partnerPillarEchart,
       //资产方
        this.fundpieData=response.fundPieData,
      this.fundxixas=response.fundXaxis,
      this.fundbarData=response.fundPillarEchart,
          //担保公司
        this.custpieData=response.custPieData,
      this.custxixas=response.custXaxis,
      this.custbarData=response.custPillarEchart,
      //每日放还款统计
      //放还款x轴数据
       this.dayRepayCountxaxis = response.repayXixas,
      this.weekRepayCountxaxis = response.weekRepayXaxis,
      this.monthRepayCountxaxis = response.monthRepayXaxis,
      this.yearRepayCountxaxis = response.yearRepayXaxis,
      //放还款数据
      this.dayfangData = response.repayfangData,
      this.dayhuanData= response.repayhuanData,
      this.weekfangData = response.weekRepayFangData,
      this.weekhuanData = response.weekRepayHuanData,
      this.monthfangData = response.monthRepayFangData,
      this.monthhuanData = response.monthRepayHuanData,
      this.yearfangData = response.yearRepayFangData,
      this.yearhuanData = response.yearRepayHuanData,


      this. weekendxaxis= response.weekend,
     this. monthendxaxis= response.monthend,
     this. yearendxaxis= response.yearend,
      // this.repayfangamount = response.repayfangData,
      // this.repayhuanamount = response.repayhuanData,
      // this.repayCountXixas = response.repayXixas,
      //在贷余额
          //echart数据
           //在贷余额和新增贷款笔数日周月年x轴数据
          this.dayxaxisData = response.homeAmountXixas,
          this.weekxaxisData = response.weekXixas,
          this.monthxaxisData = response.monthXixas,
          this.yearxaxisData = response.yearXixas,
          //在贷余额日周月年数据
          this.dayAmountData = response.eChartBalanceAmount,
          this.weekAmountData = response.weekBalanceAmount,
          this.monthAmountData = response.monthBalanceAmount,
          this.yearAmountData = response.yearBalanceAmount,
          //新增贷款笔数日周月年数据
          this.dayAddCountData = response.eChartAddCount,
          this.weekAddCountData = response.weekaddCount,
          this.monthAddCountData = response.monthaddCount,
          this.yearAddCountData = response.yearaddCount,
          //调用初始日的数据
          this.balanceAmountDay();
          this.addCountDay();
          this.repayCountDay();
          // this.amountXixas = response.homeAmountXixas,
          // this.echartBalanceAmount = response.eChartBalanceAmount,
          // this.echarTotalCount=response.eChartAddCount,
    //vintage数据
        this.stackbarXaxis = response.xaxis
        this.stackbarData = response.data
        this.legendDatas = response.legendData
       //利润统计数据
          this.profitXaxis = response.profitXAxis,
          this.profitEchartData =response.profitData,
          // 合作方堆叠的数据
            this.dayRepayAmountData = response.partnerStackData
            this.dayRepayAmountXaxis = response.partnerStackXaxis
           this.parStackData =  this.dayRepayAmountData
           this.parStackXaxis=this.dayRepayAmountXaxis

            this.weekRepayAmountData = response.weekRepayAmountData
            this.weekRepayAmountXaxis = response.weekRepayAmountXaxis
            this.monthRepayAmountData = response.monthRepayAmountData
            this.monthRepayAmountXaxis = response.monthRepayAmountXaxis
            this.yearRepayAmountData = response.yearRepayAmountData
            this.yearRepayAmountXaxis = response.yearRepayAmountXaxis



             //资金方堆叠的数据
           /* this.fundStackData = response.fundStackData
            this.fundStackXaxis = response.fundStackXaxis*/

           this.dayfundAmountData = response.fundStackData
           this.dayfundAmountXaxis = response.fundStackXaxis
           this.fundStackData =  this.dayfundAmountData
           this.fundStackXaxis=this.dayfundAmountXaxis

           this.weekfundAmountData = response.weekfundAmountData
           this.weekfundAmountXaxis = response.weekfundAmountXaxis
           this.monthfundAmountData = response.monthfundAmountData
           this.monthfundAmountXaxis = response.monthfundAmountXaxis
           this.yearfundAmountData = response.yearfundAmountData
           this.yearfundAmountXaxis = response.yearfundAmountXaxis




            this.fundStackData = response.fundStackData
            this.fundStackXaxis = response.fundStackXaxis
         //产品利润堆叠图
         this.profitRankStack = response.profitRankStack;
            this.balanceAmountEChart();
            this.totalCountEChart();
            this.barecharts();
            // this.profiteCharts();
            this.repayCountEchart();
            this.partnerDataEChart();
            this.partnerpie();
            // this.badDebteCharts();
            this.fundDataEChart();
            this.fundpie();
             this.custDataEChart();
            this.custpie();
           /*   this.fundStackEChart();
           this.parStackEChart();*/
            this.zdyehzfDay();
            this.zdyzjfDay();

          /*  this.fundStackEChart();
            this.parStackEChart();*/
            // this.spikProfitRankEChart();
          this.loading = false;
        }

      );
    },
    spikDdata(){
      this.$router.push({path: '/echart/dData'});
    },
    spikVintage(){
      this.$router.push({path: '/echart/vintage'});
    },
    spikProfit(){
      this.$router.push({path: '/echart/profitCal'});
    },
    spikpartner(){
      this.$router.push({path: '/echart/partnerEchart'});
    },
    spikfund(){
      this.$router.push({path: '/echart/fundEchart'});
    },

 spikcust(){
      this.$router.push({path: '/echart/custEchart'});
    },
    spikbadDebt(){
        this.$router.push({path: '/echart/badDebtRate'});
    },
    spikProfitRank(){
      this.$router.push({path: '/echart/profitRank'});
    },

 /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
         //  start 重置逻辑更新
      this.resetForm('queryForm')

      this.platformNoParam = ''
      this.custNoParam = ''
      this.partnerNoParam = ''
      this.fundNoParam = ''
      this.handleQuery()
      this.initSelect()
      //  end 重置逻辑更新
    },
     // 多选框选中数据
    // handleSelectionChange(selection) {
    //   this.ids = selection.map(item => item.id)
    //   this.single = selection.length !== 1
    //   this.multiple = !selection.length
    // },
    //坏账率

     badDebteCharts(){
            var myChart = this.$echarts.init(document.getElementById('chartsBadDebt'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                        legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50
                       },
                      grid: {
                              x: 220,
                              y: 70,
                              x2: 100,
                              y2: 100,
                              borderWidth: 1,
                            },
                        // title: {
                        //     left: 'left',
                        //     text: '利润测算（近12个月）',
                        //     padding: [5, 0,0, 20],
                        //      textStyle: {
                        //       fontSize: 16,
                        //       color: 'rgba(51, 51, 51, 1)',
                        //       fontWeight: "bolder"
                        //       }
                        // },
              color: ['#F15A75'],

                         xAxis: {
                           type: 'value',

                        },

                        yAxis: {

                          name: "单位（百分比）",
                           type: 'category',
                            data: this.badDebtxixas
                        },
                        series:  {
                            data: this.badDebtEchartData,
                            type:'bar',
                            // itemStyle:{
                            //   normal:{
                            //     color:function(params){
                            //         // var colorList =  ['#C88B77','#61DAB3','#E35B9C','#6676CE','#47BECF','#A67BD5','#F15A75','#F5DD67','#66CC85','#3AA1FF'];
                            //         // var colorList =  ['#3AA1FF','#66CC85','#F5DD67','#F15A75','#A67BD5','#47BECF','#6676CE','#E35B9C','#61DAB3','#C88B77'];
                            //         var colorList =  ['#3AA1FF','#66CC85','#F5DD67','#F15A75','#A67BD5','#47BECF','#6676CE','#E35B9C','#61DAB3','#C88B77'];
                            //         return colorList[params.dataIndex]
                            //     }
                            //   }
                            // }
                          }
             };
              myChart.clear()
            myChart.setOption(option);
             window.addEventListener("resize",function(){
                myChart.resize();
            });
    },
    //利润测算
     profiteCharts(){
            var myChart = this.$echarts.init(document.getElementById('charts4'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                        legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50
                       },
                      grid: {
                              x: 80,
                              y: 70,
                              x2: 60,
                              y2: 100,
                              borderWidth: 1,
                            },
                        // title: {
                        //     left: 'left',
                        //     text: '利润测算（近12个月）',
                        //     padding: [5, 0,0, 20],
                        //      textStyle: {
                        //       fontSize: 16,
                        //       color: 'rgba(51, 51, 51, 1)',
                        //       fontWeight: "bolder"
                        //       }
                        // },
                        color: ['#4472C4'],
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.profitXaxis,
                              axisLabel:{
                                  rotate : 60
                              }
                        },

                        yAxis: {
                          name: "单位（元）",
                          show: true,axisLine: {show:false}, axisTick: {show:false}, splitLine:{show:true},
                            type: 'value',
                            boundaryGap: ['2%', '100%']
                        },
                        series: this.profitEchartData
             };
              myChart.clear()
            myChart.setOption(option);
             window.addEventListener("resize",function(){
                myChart.resize();
            });
    },
     balanceAmountEChart(){
            var myChart = this.$echarts.init(document.getElementById('charts1'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                         legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {
                              x: 80,
                              y: 100,
                              x2: 60,
                              // y2: 25,
                              borderWidth: 1,
                            },
                        // title: {
                        //      left: 'left',
                        //     text: '在贷余额（近90天）',
                        //     padding: [60, 0,0, 20],
                        //       textStyle: {
                        //       fontSize: 16,
                        //       color: 'rgba(51, 51, 51, 1)',
                        //       fontWeight: "bolder"
                        //       }
                        // },

                        color: ['#ff8000'],
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.amountXixas
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},

                            },
                            top:"60px",
                        },
                        yAxis: {
                          name: "单位（万元）",
                            nameTextStyle: {
                              color: "#aaa",
                              nameLocation: "start",
                            },
                            type: 'value',
                            boundaryGap: ['0', '100%']
                        }, dataZoom: [
                             {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '93%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 1,
                            // end: 100
                            // }

                        ],
                        series:  this.echartBalanceAmount
             };
              myChart.clear()
            myChart.setOption(option);
            window.addEventListener("resize",function(){
                myChart.resize();
            });

  },
  partnerpie(){
             var myChart = this.$echarts.init(document.getElementById('echartParpie'));
             var option={

                //   toolbox: {
                //   show: true,
                //   feature: {
                //         dataView: { readOnly: false },
                //         restore: {},
                //         saveAsImage: {},
                //   }
                // },

                tooltip: {
                  trigger: 'item',
                  formatter: "{b} : {c} 万元({d}%)"
                },
                color: ['#3AA1FF','#66CC85','#F5DD67','#F15A75','#A67BD5','#47BECF','#6676CE','#E35B9C','#61DAB3','#C88B77'],
                 series:[
                    {

                        type: 'pie',
                        avoidLabelOverlap: true,
                        radius: '50%',
                        label: {
                          normal:{
                            show: true,
                            color: '#666666'
                            // position: 'center'
                          }
                         },
                         emphasis: {//选中的样式
                            borderColor: 'rgba(0,0,0,0)',
                            borderWidth: 1,
                            label: {
                                show: true//选中时不显示数据标签
                            },
                            labelLine: {
                                show: true,//选中时不显示数据标签引导线
                                // length: 50,
                                lineStyle: {
                                    width: 1,
                                    type: 'solid',
                                    color: '#666666'
                                }
                            }
                        },
                    center: ['50%', '55%'],
                      labelLine: {//设置延长线的长度
                          normal: {
                              length: 30,//设置延长线的长度
                              length2: 30,//设置第二段延长线的长度
                          }
                      },
                        data:this.partnerpieData
                    }
                 ]

             };
              myChart.clear()
            myChart.setOption(option);
             window.addEventListener("resize",function(){
                myChart.resize();
            });
        },
   partnerDataEChart(){
            var myChart = this.$echarts.init(document.getElementById('echartParBar'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                         legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {
                              x: 80,
                              // y: 100,
                              x2: 60,
                              y2: 48,
                              borderWidth: 1,
                            },
                        // title: {
                        //      left: 'left',
                        //     text: '各合作方在贷余额分布',
                        //     padding: [60, 0,0, 20],
                        //       textStyle: {
                        //       fontSize: 16,
                        //       color: 'rgba(51, 51, 51, 1)',
                        //       fontWeight: "bolder"
                        //       }
                        // },


                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.partnerxixas,
                            axisLabel: { show: true, textStyle: {color: '#666666',fontSize : 12} ,rotate : 30}
                        },
                        //  toolbox: {
                        //     show: true,
                        //     feature: {
                        //         dataZoom: {
                        //                 yAxisIndex: 'none'
                        //                   },
                        //         dataView: { readOnly: false },
                        //         // 柱状图或者折线图展示
                        //         magicType: { type: ['line', 'bar'] },

                        //         restore: {},
                        //         saveAsImage: {},

                        //     },
                        //     top:"60px",
                        // },
                        yAxis: {
                          name: "单位（万元）",
                            nameTextStyle: {
                              color: "#aaa",
                              nameLocation: "start",
                            },
                            type: 'value',
                            show: true,axisLine: {show:false}, axisTick: {show:false}, splitLine:{show:true},
                            boundaryGap: ['2%', '100%'],

                              max:function (value) {

                                return Math.trunc(value.max*1.33) ;
                            },
                            min:function (value) {
                                return 0;
                            }
                        },


                        series: [
                          {
                            data: this.partnerbarData,
                            type:'bar',
                            itemStyle:{
                              normal:{
                                color:function(params){
                                    var colorList =  ['#3AA1FF','#66CC85','#F5DD67','#F15A75','#A67BD5','#47BECF','#6676CE','#E35B9C','#61DAB3','#C88B77'];
                                    return colorList[params.dataIndex]
                                }
                              }
                            }
                          }
                        ]
             };
              myChart.clear()
            myChart.setOption(option);
             window.addEventListener("resize",function(){
                myChart.resize();
            });

  },
   fundpie(){
             var myChart = this.$echarts.init(document.getElementById('echartfundpie'));
             var option={

                //   toolbox: {
                //   show: true,
                //   feature: {
                //         dataView: { readOnly: false },
                //         restore: {},
                //         saveAsImage: {},
                //   }
                // },

                tooltip: {
                  trigger: 'item',
                  formatter: "{b} : {c} 万元 ({d}%)"
                },
               color: ['#3AA1FF','#66CC85','#F5DD67','#F15A75','#A67BD5','#47BECF','#6676CE','#E35B9C','#61DAB3','#C88B77'],
                 series:[
                    {

                        type: 'pie',
                        avoidLabelOverlap: true,
                        radius: '50%',
                        label: {
                          normal:{
                            show: true,
                            color: '#666666'
                            // position: 'center'
                          }
                         },
                         emphasis: {//选中的样式
                            borderColor: 'rgba(0,0,0,0)',
                            borderWidth: 1,
                            label: {
                                show: true//选中时不显示数据标签
                            },
                            labelLine: {
                                show: true,//选中时不显示数据标签引导线
                                // length: 50,
                                lineStyle: {
                                    width: 1,
                                    type: 'solid',
                                    color: '#666666'
                                }
                            }
                        },
                    center: ['50%', '50%'],
                      labelLine: {//设置延长线的长度
                          normal: {
                              length: 30,//设置延长线的长度
                              length2: 30,//设置第二段延长线的长度
                          }
                      },
                        data:this.fundpieData
                    }
                 ]

             };

              myChart.clear()
            myChart.setOption(option);
             window.addEventListener("resize",function(){
                myChart.resize();
            });
        },
   fundDataEChart(){
            var myChart = this.$echarts.init(document.getElementById('echartfundBar'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                         legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                //        color: ['rgb(203,155,255)', 'rgb(149,162,255)', 'rgb(58,186,255)',
                // 'rgb(119,168,249)', 'rgb(235,161,159)'],
                       grid: {
                              x: 80,
                              // y: 100,
                              x2: 60,
                              y2: 115,
                              borderWidth: 1,
                            },
                        // title: {
                        //      left: 'left',
                        //     text: '各合作方在贷余额分布',
                        //     padding: [60, 0,0, 20],
                        //       textStyle: {
                        //       fontSize: 16,
                        //       color: 'rgba(51, 51, 51, 1)',
                        //       fontWeight: "bolder"
                        //       }
                        // },

                        color: ['#ff8000'],
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.fundxixas,
                            axisLabel: { show: true, textStyle: {color: '#666666',fontSize : 12} ,rotate : 25}
                        },
                        //  toolbox: {
                        //     show: true,
                        //     feature: {
                        //         dataZoom: {
                        //                 yAxisIndex: 'none'
                        //                   },
                        //         dataView: { readOnly: false },
                        //         // 柱状图或者折线图展示
                        //         magicType: { type: ['line', 'bar'] },

                        //         restore: {},
                        //         saveAsImage: {},

                        //     },
                        //     top:"60px",
                        // },
                        yAxis: {
                          name: "单位（万元）",
                            nameTextStyle: {
                              color: "#aaa",
                              nameLocation: "start",
                            },
                            type: 'value',
                            show: true,axisLine: {show:false}, axisTick: {show:false}, splitLine:{show:true},
                            boundaryGap: ['2%', '100%'],

                              max:function (value) {
                                return Math.trunc(value.max *1.33) ;
                            },
                            min:function (value) {
                                return 0;
                            }
                        },


                        series: {
                            data: this.fundbarData,
                            type:'bar',
                            itemStyle:{
                              normal:{
                                color:function(params){
                                    var colorList =  ['#3AA1FF','#66CC85','#F5DD67','#F15A75','#A67BD5','#47BECF','#6676CE','#E35B9C','#61DAB3','#C88B77'];
                                    return colorList[params.dataIndex]
                                }
                              }
                            }
                          }
             };
              myChart.clear()
            myChart.setOption(option);
             window.addEventListener("resize",function(){
                myChart.resize();
            });

  },
  custpie(){
             var myChart = this.$echarts.init(document.getElementById('echartCustpie'));
             var option={

                //   toolbox: {
                //   show: true,
                //   feature: {
                //         dataView: { readOnly: false },
                //         restore: {},
                //         saveAsImage: {},
                //   }
                // },

                tooltip: {
                  trigger: 'item',
                  formatter: "{b} : {c} 万元 ({d}%)"
                },
               color: ['#3AA1FF','#66CC85','#F5DD67','#F15A75','#A67BD5','#47BECF','#6676CE','#E35B9C','#61DAB3','#C88B77'],
                 series:[
                    {

                        type: 'pie',
                        avoidLabelOverlap: true,
                        radius: '50%',
                        label: {
                          normal:{
                            show: true,
                            color: '#666666'
                            // position: 'center'
                          }
                         },
                         emphasis: {//选中的样式
                            borderColor: 'rgba(0,0,0,0)',
                            borderWidth: 1,
                            label: {
                                show: true//选中时不显示数据标签
                            },
                            labelLine: {
                                show: true,//选中时不显示数据标签引导线
                                // length: 50,
                                lineStyle: {
                                    width: 1,
                                    type: 'solid',
                                    color: '#666666'
                                }
                            }
                        },
                    center: ['50%', '50%'],
                      labelLine: {//设置延长线的长度
                          normal: {
                              length: 30,//设置延长线的长度
                              length2: 30,//设置第二段延长线的长度
                          }
                      },
                        data:this.custpieData
                    }
                 ]

             };

              myChart.clear()
            myChart.setOption(option);
             window.addEventListener("resize",function(){
                myChart.resize();
            });
        },
        custDataEChart(){
            var myChart = this.$echarts.init(document.getElementById('echartCustBar'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                         legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {
                              x: 80,
                              // y: 100,
                              x2: 60,
                              y2: 115,
                              borderWidth: 1,
                            },
                        color: ['#ff8000'],
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.custxixas,
                            axisLabel: { show: true, textStyle: {color: '#666666',fontSize : 12} ,rotate : 25}
                        },
                        //  toolbox: {
                        //     show: true,
                        //     feature: {
                        //         dataZoom: {
                        //                 yAxisIndex: 'none'
                        //                   },
                        //         dataView: { readOnly: false },
                        //         // 柱状图或者折线图展示
                        //         magicType: { type: ['line', 'bar'] },
                        //         restore: {},
                        //         saveAsImage: {},
                        //     },
                        //     top:"60px",
                        // },
                        yAxis: {
                          name: "单位（万元）",
                            nameTextStyle: {
                              color: "#aaa",
                              nameLocation: "start",
                            },
                            type: 'value',
                            show: true,axisLine: {show:false}, axisTick: {show:false}, splitLine:{show:true},
                            boundaryGap: ['2%', '100%'],

                              max:function (value) {
                                return Math.trunc(value.max *1.33) ;
                            },
                            min:function (value) {
                                return 0;
                            }
                        },


                        series: {
                            data: this.custbarData,
                            type:'bar',
                            itemStyle:{
                              normal:{
                                color:function(params){
                                    var colorList =  ['#3AA1FF','#66CC85','#F5DD67','#F15A75','#A67BD5','#47BECF','#6676CE','#E35B9C','#61DAB3','#C88B77'];
                                    return colorList[params.dataIndex]
                                }
                              }
                            }
                          }
             };
              myChart.clear()
            myChart.setOption(option);
             window.addEventListener("resize",function(){
                myChart.resize();
            });

          },



repayCountEchart(){
            var myChart = this.$echarts.init(document.getElementById('repayCountEchartDiv'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                        legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50,


                       },
                       grid: {
                              x: 80,
                              y: 100,
                              x2: 60,
                              y2: 110,
                              borderWidth: 1,
                            },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.repayCountXixas
                        },

                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},
                            },
                            top:"60px",
                        },
                        yAxis: {
                           name: "单位（万元）",
                            nameTextStyle: {
                              color: "#aaa",
                              nameLocation: "start",
                            },
                            type: 'value',
                            show: true,axisLine: {show:false}, axisTick: {show:false}, splitLine:{show:true},
                            boundaryGap: ['0%', '100%']
                        },
                         dataZoom: [
                             {
                            bottom:55,
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '93%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 1,
                            // end: 100
                            // }

                        ],
                         series: [{
                           name:'新增放款',
                            data: this.repayfangamount,
                            type:'bar',
                            itemStyle:{
                              normal:{
                                color:'#FF8000'
                              }
                            }
                          },
                          {
                              name:'新增还款',
                            data: this.repayhuanamount,
                            type:'bar',
                            itemStyle:{
                              normal:{
                                color:'#3AA1FF'
                              }
                            }
                          }
                         ]
                        // series: this.echarTotalCount
             };
              myChart.clear()
            myChart.setOption(option);
             window.addEventListener("resize",function(){
                myChart.resize();
            });

    },



  totalCountEChart(){
            var myChart = this.$echarts.init(document.getElementById('charts2'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                        legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {
                              x: 80,
                              y: 100,
                              x2: 60,
                              y2: 110,
                              borderWidth: 1,
                            },
                        // title: {
                        //     left: 'left',
                        //     text: '每日新增贷款笔数（近90天）',
                        //     padding: [60, 0,0, 0],
                        //      textStyle: {
                        //       fontSize: 16,
                        //       color: 'rgba(51, 51, 51, 1)',
                        //       fontWeight: "bolder"
                        //       }
                        // },
                        color: ['#4472C4'],
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.addcountXixas
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},
                            },
                            top:"60px",
                        },
                        yAxis: {
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        },
                         dataZoom: [
                             {
                            bottom:55,
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '93%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 1,
                            // end: 100
                            // }

                        ],
                        series: this.echarTotalCount
             };
              myChart.clear()
            myChart.setOption(option);
             window.addEventListener("resize",function(){
                myChart.resize();
            });

    },

     barecharts(){
        var myChart = this.$echarts.init(document.getElementById('charts3'));
        var option = {
              tooltip: {
                    trigger: 'axis',
                    position: function (pt) {
                      return [pt[0], '10%'];
                    },

                  },

                   grid: {
                              x: 130,
                              y: 70,
                              x2: 60,
                              y2: 100,
                              borderWidth: 1,
                            },
                   legend: {
                     type:'plain',
                     data: this.legendDatas,
                     bottom: "3%",
                     left: "80"
                   },
                  // title: {
                  //   left: 'left',
                  //   text: 'Vintage（30+ ）',
                  //   padding: [5, 0,0, 30],
                  //    textStyle: {
                  //             fontSize: 16,
                  //             color: 'rgba(51, 51, 51, 1)',
                  //             fontWeight: "bolder"
                  //     }
                  // },
                  xAxis: {

                    //X轴数据格式
                    type: 'category',
                    //留白策略
                    boundaryGap: true,
                    data: this.stackbarXaxis
                  },
                  yAxis: {
                    show: true,axisLine: {show:false}, axisTick: {show:false}, splitLine:{show:true},
                    type: 'value',
                    min:0,  //取0为最小刻度
                    max: 1, //取为最大刻度
         
                    min:'dataMin', //取最小值为最小刻度
                    max: 'dataMax', //取最大值为最大刻度          
                   
                    scale: true, //自适应
                    boundaryGap: ['1%', '100%']
                  },
                  series: this.stackbarData
        };
        myChart.clear()
        myChart.setOption(option,true);
         window.addEventListener("resize",function(){
                myChart.resize();
            });

    },
     fundStackEChart(){
            var myChart = this.$echarts.init(document.getElementById('fundStack'));
            var option={
                  tooltip: {
                            trigger: 'item',
                            axisPointer: {
                              type: 'cross',
                              label: {
                                backgroundColor: '#6a7985'
                              }
                            }
                        },
                       legend:{

                        type:'scroll',
                          left: 'center',
                          top: 50,
                       },
                       grid: {
                              x: 110,
                              y: 100,
                              x2: 20,
                              // y2: 25,
                              borderWidth: 1,
                            },

                         color: ['#3AA1ff','#66cc85','#F5dd67','#F15A75','#A67BD5','#47BECF','#6676ce','#e35b9c','#61dab3','#c88b77'],
                        xAxis: [
                            {
                              type: 'category',
                              boundaryGap: false,
                              data: this.fundStackXaxis,
                              axisLabel: { show: true, textStyle: {color: '#666666',fontSize : 12} ,rotate : 25}
                            }
                          ],
                          yAxis: [
                            {
                              type: 'value',
                              name: "单位（万元）",
                              max:function (value) {
                                  return (value.max + value.max/10).toFixed(2);
                              },
                              min:function (value) {
                                  return (value.min + value.min/10).toFixed(2);
                              }
                            }
                          ],
                        series:  this.fundStackData
             };
              myChart.clear()
            myChart.setOption(option);
       window.addEventListener("resize",function(){
         myChart.resize();
       });
  },
    fundStackEChartas(){
            var myChart = this.$echarts.init(document.getElementById('fundStackas'));
            var option={
                  tooltip: {
                            trigger: 'item',
                            axisPointer: {
                              type: 'cross',
                              label: {
                                backgroundColor: '#6a7985'
                              }
                            }
                        },
                        legend:{

                        type:'scroll',
                          left: 'center',
                          top: 0,
                       },

              grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
              },

              color: ['#3AA1ff','#66cc85','#F5dd67','#F15A75','#A67BD5','#47BECF','#6676ce','#e35b9c','#61dab3','#c88b77'],
                        xAxis: [
                            {
                              type: 'category',
                              boundaryGap: false,
                              data: this.fundStackXaxis,
                              axisLabel: { show: true, textStyle: {color: '#666666',fontSize : 12} ,rotate : 25}
                            }
                          ],
                          yAxis: [
                            {
                              type: 'value',
                              name: "单位（万元）",
                              max:function (value) {
                                  return (value.max + value.max/10).toFixed(2);
                              },
                              min:function (value) {
                                  return (value.min + value.min/10).toFixed(2);
                              }
                            }
                          ],
                        series:  this.fundStackData
             };


              myChart.clear()
           setTimeout(() => {
            myChart.setOption(option);
      window.addEventListener("resize",function(){
        myChart.resize();
      });
           }, 1000);
  },

   parStackEChart(){
            var myChart = this.$echarts.init(document.getElementById('parStack'));
            var option={
                  tooltip: {
                            trigger: 'item',
                            axisPointer: {
                              type: 'cross',
                              label: {
                                backgroundColor: '#6a7985'
                              }
                            }
                        },

                        legend:{
                          type:'scroll',
                          left: 'center',
                          top: 50,
                        },
                       grid: {
                              x: 110,
                              y: 100,
                              x2: 20,
                              // y2: 25,
                              borderWidth: 1,
                            },

              color: ['#3AA1ff','#66cc85','#F5dd67','#F15A75','#A67BD5','#47BECF','#6676ce','#e35b9c','#61dab3','#c88b77'],
                        xAxis: [
                            {
                              type: 'category',
                              boundaryGap: false,
                              data: this.parStackXaxis,
                              axisLabel: { show: true, textStyle: {color: '#666666',fontSize : 12} ,rotate : 25}
                            }
                          ],
                          yAxis: [
                            {
                              type: 'value',
                              name: "单位（万元）",
                              max:function (value) {
                                  return (value.max + value.max/10).toFixed(2);
                              },
                              min:function (value) {
                                  return (value.min + value.min/10).toFixed(2);
                              }
                            }
                          ],
                        series:  this.parStackData
             };
              myChart.clear()
            myChart.setOption(option);
     window.addEventListener("resize",function(){
       myChart.resize();
     });
  },
 parStackEChartas(){
            var myChart = this.$echarts.init(document.getElementById('parStackas'));
            var option={
                  tooltip: {
                            trigger: 'item',
                            axisPointer: {
                              type: 'cross',
                              label: {
                                backgroundColor: '#6a7985'
                              }
                            }
                        },

                        legend:{},
              grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
              },

              color: ['#3AA1ff','#66cc85','#F5dd67','#F15A75','#A67BD5','#47BECF','#6676ce','#e35b9c','#61dab3','#c88b77'],
                        xAxis: [
                            {
                              type: 'category',
                              boundaryGap: false,
                              data: this.parStackXaxis,
                              axisLabel: { show: true, textStyle: {color: '#666666',fontSize : 12} ,rotate : 25}
                            }
                          ],
                          yAxis: [
                            {
                              type: 'value',
                              name: "单位（万元）",
                              max:function (value) {
                                  return (value.max + value.max/10).toFixed(2);
                              },
                              min:function (value) {
                                  return (value.min + value.min/10).toFixed(2);
                              }
                            }
                          ],
                        series:  this.parStackData
             };


              myChart.clear()
             setTimeout(() => {
              myChart.setOption(option);
   window.addEventListener("resize",function(){
     myChart.resize();
   });
             }, 1000);
  },

    spikProfitRankEChart(){
      var myChart = this.$echarts.init(document.getElementById('profitRank'));
      var option={
        tooltip: {
          trigger: 'axis',
        },

        grid: {
          x: 110,
          y: 100,
          x2: 20,
          // y2: 25,
          borderWidth: 1,
        },

        color: ['#3AA1FF','#66CC85','#F5DD67','#F15A75','#A67BD5','#47BECF','#6676CE','#E35B9C','#61DAB3','#C88B77'],
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: this.profitXaxis,
            axisLabel: { show: true, textStyle: {color: '#666666',fontSize : 12} ,rotate : 25}
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: "单位（万元）",
            max:function (value) {
              return (value.max + value.max/10).toFixed(2);
            },
            min:function (value) {
              return (value.min + value.min/10).toFixed(2);
            }
          }
        ],
        series:  this.profitRankStack
      };
      myChart.clear()
      myChart.setOption(option);
        window.addEventListener("resize",function(){
                myChart.resize();
            });

    },


handleClose(done) {
        this.$confirm('确认关闭？')
          .then(_ => {
            done();
          })
          .catch(_ => {});
      }
  }


}
</script>
<style>
.el-row {
    margin-bottom: 20px;
  }
   .grid-content {
    /* border-radius: 10px;
    height: 50px;
    line-height: 14px; */
    color:#9D9D9D;
    /* font-weight:bold; */
    font-size:14px;
    text-align: center;
    margin-left: 24px;
  }
  .grid-contentfont{
      color:#333333;
    font-weight:bold;
    font-size:14px;
    margin-left: 16px;
  }
  .grid-contentcol{
    height: 20px;
    line-height: 40px;
    left: 30px;
  }
   .grid-col1 {
    border-radius: 4px;
    height: 36px;

  }
  .bg-purple {
    background: #9D9D9D;
  }
  #col-line {
      float: left;
      width: 1px;
      height: 60px;
      background: 	#E6E6E6;
    }

.span{
    color:#ff8000;
    font-weight:bold;
    margin-left: 24px;
    font-size:24px;
  }
.spancol{
    color:#333333;
    font-weight:bold;
    font-size:24px;
    display: inline-block;
    margin-left: 24px;
    /* padding-top:10px; */

}
.amounting{
  font-size:14px;
    color:#9D9D9D;
    margin-left: 24px;
    margin-top:10px;
    display:block;
}
.spancol2{
    font-size:14px;
    color:#9D9D9D;
    margin-left: 24px;
    display:block;
}
.echartspan{
  color: 	#007fff;
  font-size:12px;
  /* font-weight:bold; */
  /* margin-left: 60px; */
}
.balancediv{
  width: 100px;
  height:35px;
}
.item {
      margin: 4px;
    }
.divsecend {
   /* border-radius: px; */
    min-height: 100px;
    background-color: #FFFFFF;
}
.spanfont1 {
  color: 	#666666;
  font-size:16px;
  font-weight:normal;
  font-family:"Microsoft YaHei";
  margin-left: 24px;
}

.inner{
  width: 49%;
  height: 12%;
  background: #FFFFFF;
  margin: 4px;
}
.inneraaa{
  width: 24.3%;
  height: 100px;
  background: #FFFFFF;
  margin: 4px;
}
.innerone{
  width: 24.3%;
  height: 100px;
  background: #FFFFFF;
  margin: 4px;
}
.dialogspan{
  font-size:10px;
  color: 	#afadad;
  /* font-weight:bold; */
}
</style>



