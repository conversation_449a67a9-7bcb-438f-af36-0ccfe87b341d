<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="98px"
    >
      <el-form-item label="员工姓名" prop="personnelName">
        <el-input
          v-model="queryParams.personnelName"
          placeholder="请输入员工姓名"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单据类型" class="form-item" prop="processType">
        <el-select
          v-model="queryParams.processType"
          placeholder="请选择单据类型"
          clearable
          @clear="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.document_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态" class="form-item" prop="processState">
        <el-select
          v-model="queryParams.processState"
          placeholder="请选择任务状态"
          clearable
          @clear="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.approval_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-divider></el-divider>
    <el-row :gutter="10" class="mb8 mt8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="configList"
    >
      <el-table-column
        type="index"
        label="序号"
        width="50"
        :index="columnIndex"
      />
      <el-table-column
        label="流程ID"
        align="center"
        prop="processId"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="标题" align="center" prop="processName" />
      <el-table-column label="任务节点" align="center" prop="processNode" >
      <template #default="{ row }">
          <div size="mini" type="text">
            {{ dict.label.approval_status[row.processState]=='完成'?'结束':row.processNode }}
          </div>
        </template>
         </el-table-column>
      <el-table-column label="状态" align="center">
        <template #default="{ row }">
          <div size="mini" type="text">
            {{ dict.label.approval_status[row.processState] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="发起人"
        align="center"
        prop="sponsor"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="发起时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="{ row }">
          <span>{{ parseTime(row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button size="mini" type="text" @click="viewProcessId(row)"
            >查看流程</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getProcessGetlist } from "@/api/personnel/history";
export default {
  name: "History",
  dicts: ["document_type","approval_status"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      configList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        personnelName: undefined,
        processState: undefined,
        processType: undefined,
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },

    columnIndex(index) {
      return (
        index + 1 + (this.queryParams.pageNum - 1) * this.queryParams.pageSize
      );
    },
    /** 查询参数列表 */
    async getList() {
      this.loading = true;
      const { rows, total } = await getProcessGetlist(this.queryParams);
      this.configList = rows;
      console.log(this.configList,22222222)
      this.total = total;
      this.loading = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    viewProcessId(value) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: value.processId,
          businessId: value.processId,
          myActiviteType: true,
        },
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "personnel/process/export",
        {
          ...this.queryParams,
        },
        `人员审批历史.xlsx`
      );
    },
  },
};
</script>
