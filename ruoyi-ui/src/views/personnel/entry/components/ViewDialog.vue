<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="innerValue"
      width="1050px"
      title="人员选择"
      @close="handleClose"
      @open="handleOpen"
    >
      <div class="content">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          label-width="98px"
        >
          <el-form-item label="人员名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入人员名称"
              clearable
              size="small"
              style="width: 240px"
              @keyup.enter.native="handleQuery"
              @clear="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
        <el-table
          :data="tableList"
          row-key="id"
          @selection-change="handleSelectionChange"
          ref="multipleTable"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
            reserve-selection
            fixed="left"
          />
          <el-table-column label="人员名称" align="center" prop="nickName" />
          <el-table-column label="部门" align="center" prop="deptName" />
          <el-table-column
            label="企业邮箱"
            align="center"
            prop="email"
          />
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getHandleOpen"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button type="primary" @click="onSave">确定</el-button>
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import { systemUserList } from "@/api/personnel/entry";


export default {
  mixins: [vModelMixin],

  props: {
  },
  watch: {},
  data() {
    return {
      total: 0,
      tableList: [],
      queryParams: {
        name: "",
        pageNum: 1,
        pageSize: 10,
      },
      multipleSelection: [],
    };
  },
  mounted() {},
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getHandleOpen();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
    onSave() {
      if (this.multipleSelection.length > 1) {
        this.$modal.msgError("至多勾选一项");
        return;
      }

      this.innerValue = false;
      this.$emit("on-save-success", this.multipleSelection);
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    async handleOpen() {
      this.queryParams.pageNum = 1;
      this.multipleSelection = [];
      this.getHandleOpen();
      this.$nextTick(() => {
        this.$refs.multipleTable.clearSelection();
      });
    },
    async getHandleOpen() {
      this.tableList = [];
      const {rows,total} = await systemUserList({
        ...this.queryParams,
        nickName: this.queryParams.name,
      });

      this.tableList = rows;
      this.total = total||0;
    },
    handleClose() {},
  },
};
</script>
<style lang="less" scoped>
.content {
  max-height: 70vh;
  padding-right: 20px;
  .flex {
    display: flex;
    justify-content: space-between;

    .form-item {
      width: 48%;
    }
  }
  .flex-one {
    width: 70%;
    display: flex;

    ::v-deep .el-form-item {
      width: 100%;
    }
  }
}
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>