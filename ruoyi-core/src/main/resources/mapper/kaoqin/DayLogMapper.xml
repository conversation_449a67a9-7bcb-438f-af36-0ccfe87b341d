<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.kaoqin.mapper.DayLogMapper">

    <resultMap type="DayLog" id="DayLogResult">
        <result property="id"    column="id"    />
        <result property="logDate"    column="log_date"    />
        <result property="isAllDay"    column="is_all_day"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="logContent"    column="log_content"    />
        <result property="logStatus"    column="log_status"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="dataFormat"    column="data_format"    />

        <collection property="logContentList" column = "id = id" select = "selectDayLogContentList"/>
    </resultMap>


    <resultMap type="DayLogListVo" id="DayLogListVoResult">
        <result property="id"    column="id"    />
        <result property="logDate"    column="log_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="reportTime"    column="report_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deptName"    column="dept_name"    />
        <result property="nickName"    column="nick_name"    />
<!--        <collection property="dayLogList" column = "{createBy = create_by,logDate = log_date}" select="selectDayLog"/>-->
    </resultMap>

<!--    <resultMap type="DayLog" id="DayLogResult">-->
<!--        <result property="startTime"    column="start_time"    />-->
<!--        <result property="endTime"    column="end_time"    />-->
<!--        <result property="logContent"    column="log_content"    />-->
<!--    </resultMap>-->



    <sql id="selectDayLogVo">
        select id, log_date, is_all_day, start_time, end_time, log_content, log_status, is_delete,remark, create_by, create_time, update_by, update_time from kq_day_log
    </sql>

    <select id="selectDayLogList" parameterType="DayLog" resultMap="DayLogResult">
        <include refid="selectDayLogVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="logDate != null "> and log_date = #{logDate}</if>
            <if test="isAllDay != null  and isAllDay != ''"> and is_all_day = #{isAllDay}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="logContent != null  and logContent != ''"> and log_content = #{logContent}</if>
            <if test="logStatus != null  and logStatus != ''"> and log_status = #{logStatus}</if>
        </where>
    </select>

    <select id="selectDayLog" parameterType="DayLog" resultMap="DayLogResult">
        select kdl.id,kdl.start_time,kdl.end_time,kdl.log_content,kdl.is_all_day,
            CASE
            WHEN COALESCE(kdl.log_content, '') = '' THEN 2
            ELSE 1
            END AS data_format
        from kq_day_log kdl
        left join kq_day_log_content kdlc on kdlc.main_id = kdl.id
        where create_by = #{createBy} and log_date = #{logDate}
        group by kdl.id
    </select>

    <select id="selectDayLogOfList" parameterType="DayLogListVo" resultMap="DayLogResult">
        select kdl.id,kdl.start_time,kdl.end_time,kdl.log_content,kdl.is_all_day,kdl.log_date,kdl.create_by,
               CASE
                   WHEN COALESCE(kdl.log_content, '') = '' THEN 2
                   ELSE 1
                   END AS data_format
        from kq_day_log kdl
        left join kq_day_log_content kdlc on kdlc.main_id = kdl.id
        <where>
        <if test="createByList != null and createByList.size() > 0">
            create_by in
            <foreach item="createBy" collection="createByList" open="(" close=")" separator=",">
                #{createBy}
            </foreach>
        </if>
        <if test="createByList != null and createByList.size() > 0">
            and log_date in
            <foreach item="logDate" collection="logDateList" open="(" close=")" separator=",">
                #{logDate}
            </foreach>
        </if>
        </where>
        group by kdl.id
    </select>

    <select id="selectDayLogVoList" parameterType="DayLogListVo" resultMap="DayLogResult">
        select kdl.id, log_date, is_all_day, start_time, end_time, kdl.log_content, log_status
             , is_delete,remark, create_by, create_time, update_by, update_time,
                CASE
                WHEN COALESCE(kdl.log_content, '') = '' THEN 2
                ELSE 1
                END AS data_format,
                '' as logContentCondition
        from kq_day_log kdl
        left join kq_day_log_content kdlc on kdlc.main_id = kdl.id
        <where>
            and is_delete = '1'
            <if test="monthCondition != null "> and log_date LIKE concat('%',#{monthCondition},'%')</if>
            <if test="createBy != null">and create_by = #{createBy}</if>
            <if test="logContent != null  and logContent != ''"> and (kdl.log_content LIKE concat('%',#{logContent},'%') or kdlc.log_content LIKE concat('%',#{logContent},'%'))</if>
            <if test="logStatus != null and logStatus != ''">and log_status = #{logStatus}</if>
        </where>
        group by kdl.id
    </select>

    <select id="selectDayLogById" parameterType="String" resultMap="DayLogResult">
        <include refid="selectDayLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertDayLog" parameterType="DayLog" useGeneratedKeys="true" keyProperty="id">
        insert into kq_day_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="logDate != null">log_date,</if>
            <if test="isAllDay != null">is_all_day,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="logContent != null">log_content,</if>
            <if test="logStatus != null">log_status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="logDate != null">#{logDate},</if>
            <if test="isAllDay != null">#{isAllDay},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="logContent != null">#{logContent},</if>
            <if test="logStatus != null">#{logStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <update id="updateDayLog" parameterType="DayLog">
        update kq_day_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="logDate != null">log_date = #{logDate},</if>
            <if test="isAllDay != null">is_all_day = #{isAllDay},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="logContent != null">log_content = #{logContent},</if>
            <if test="logStatus != null">log_status = #{logStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDayLogById" parameterType="Long">
        delete from kq_day_log where id = #{id}
    </delete>

    <delete id="deleteDayLogByIds" parameterType="String">
        delete from kq_day_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="getDayLogList" parameterType="DayLogListVo" resultType="DayLogListVo">
        SELECT date,
            CASE
            WHEN DAYOFWEEK(date) = 1 THEN '星期天'
            WHEN DAYOFWEEK(date) = 2 THEN '星期一'
            WHEN DAYOFWEEK(date) = 3 THEN '星期二'
            WHEN DAYOFWEEK(date) = 4 THEN '星期三'
            WHEN DAYOFWEEK(date) = 5 THEN '星期四'
            WHEN DAYOFWEEK(date) = 6 THEN '星期五'
            WHEN DAYOFWEEK(date) = 7 THEN '星期六'
            END AS weekday,
            CASE
            WHEN DAYOFWEEK(date) IN (1,7) THEN '1'
            ELSE '2'
            END AS holiday
            FROM (
            SELECT #{monthStart} + INTERVAL a + b DAY AS date
            FROM (
            SELECT 0 AS a UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION
            SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9
            ) d,
            (
            SELECT 0 AS b UNION SELECT 10 UNION SELECT 20 UNION SELECT 30 UNION SELECT 40
            UNION SELECT 50 UNION SELECT 60 UNION SELECT 70 UNION SELECT 80 UNION SELECT 90
            ) m
            ) day_range
            WHERE date BETWEEN #{monthStart} AND #{monthEnd}
        order by date
        <where>
            <if test="logDate != null "> and log_date = #{logDate}</if>
            <if test="isAllDay != null  and isAllDay != ''"> and is_all_day = #{isAllDay}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="logContent != null  and logContent != ''"> and log_content = #{logContent}</if>
            <if test="logStatus != null  and logStatus != ''"> and log_status = #{logStatus}</if>
        </where>
    </select>

    <update id="replaceDayLogBatch" parameterType="java.util.List">
        REPLACE INTO kq_day_log
            (id, log_date, is_all_day, start_time, end_time, log_content, log_status, is_delete, remark, create_by, create_time, update_by, update_time)
        VALUES
        <foreach item="item" collection="list" separator=",">
            (
            #{item.id},
            #{item.logDate},
            #{item.isAllDay},
            #{item.startTime},
            #{item.endTime},
            #{item.logContent},
            #{item.logStatus},
            #{item.isDelete},
            #{item.remark},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime}
            )
        </foreach>
    </update>

    <insert id="insertDayLogBatch" parameterType="java.util.List">
        INSERT INTO kq_day_log (log_date, is_all_day, start_time, end_time, log_content, log_status, remark, create_by, create_time)
        VALUES
        <foreach item="item" collection="list" separator=",">
            (
            #{item.logDate},
            #{item.isAllDay},
            #{item.startTime},
            #{item.endTime},
            #{item.logContent},
            #{item.logStatus},
            #{item.remark},
            #{item.createBy},
            #{item.createTime}
            )
        </foreach>
    </insert>

    <select id="getDayLogListOfLeader" parameterType="DayLogListVo" resultMap="DayLogListVoResult">
        select
            kdl.id,kdl.log_date,kdl.create_by,kdl.create_time,user.user_id,sup.post_id,dept.dept_id,dept.unit_id,dept_name,nick_name,
            CASE
                WHEN kdl.update_time IS NOT NULL THEN kdl.update_time
                ELSE kdl.create_time
            END AS report_time,
            #{logContent} as logContentCondition
        from
            kq_day_log kdl
        left join sys_user user on kdl.create_by = user.user_name
        left join sys_user_post sup on sup.user_id = user.user_id and home_post = 0
        left join sys_post post on post.post_id = sup.post_id
        left join sys_dept dept on dept.dept_id = post.dept_id
        inner join kq_day_log_content kdlc on kdlc.main_id = kdl.id
        <where>
            and kdl.log_status = 2
            <if test="nickName != null and nickName !=''">
                and nick_name like concat('%', #{nickName}, '%')
            </if>
            <if test="deptId != null and deptId != ''">
                and dept.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != ''">
                and user.user_id = #{userId}
            </if>
            <if test="deptName != null and deptName != ''">
                and dept.dept_name like concat('%', #{deptName}, '%')
            </if>
            <if test="logContent != null  and logContent != ''"> and (kdl.log_content LIKE concat('%',#{logContent},'%') or kdlc.log_content LIKE concat('%',#{logContent},'%'))</if>
            <if test="groupUserIdList != null and groupUserIdList.size() > 0">
                and user.user_id in
                <foreach item="groupUserId" collection="groupUserIdList" open="(" close=")" separator=",">
                    #{groupUserId}
                </foreach>
            </if>
            <if test="reportTimeStart != null">
                and kdl.create_time &gt;= #{reportTimeStart}
            </if>
            <if test="reportTimeEnd != null">
                and kdl.create_time &lt;= #{reportTimeEnd}
            </if>
            <if test="logDateStart != null">
                and kdl.log_date &gt;= #{logDateStart}
            </if>
            <if test="logDateEnd != null">
                and kdl.log_date &lt;= #{logDateEnd}
            </if>
            <trim prefix="and (" suffix=")" prefixOverrides="and|or">
                <if test="createBy != null and createBy != ''">
                    or kdl.create_by = #{createBy}
                </if>
                <if test="unitIds != null and unitIds.size() > 0">
                    or dept.unit_id in
                    <foreach item="unitId" collection="unitIds" open="(" close=")" separator=",">
                        #{unitId}
                    </foreach>
                </if>
                <if test="deptIds != null and deptIds.size() > 0">
                    or dept.dept_id in
                    <foreach item="deptId" collection="deptIds" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
                <if test="createByList != null and createByList.size() > 0">
                    or kdl.create_by in
                    <foreach item="createBy" collection="createByList" open="(" close=")" separator=",">
                        #{createBy}
                    </foreach>
                </if>
            </trim>
        </where>
        GROUP BY kdl.create_by,kdl.log_date
        ORDER BY kdl.create_time DESC
    </select>

    <select id="selectDayLogContentList"  resultType="DayLogContent">
        select id, main_id, log_content from kq_day_log_content
        where main_id = #{id}
    </select>
</mapper>
