package org.ruoyi.core.kaoqin.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserPost;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import org.hamcrest.core.Is;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.kaoqin.domain.*;
import org.ruoyi.core.kaoqin.domain.vo.*;
import org.ruoyi.core.kaoqin.mapper.DayLogMapper;
import org.ruoyi.core.kaoqin.service.*;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesVo;
import org.ruoyi.core.personnel.service.IPersonnelArchivesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.system.service.impl.SysDeptServiceImpl.buildDeptChain;
import static org.ruoyi.core.kaoqin.service.impl.MonthLogMainServiceImpl.findSubordinates;

/**
 * 日志填报Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class DayLogServiceImpl implements IDayLogService
{
    @Autowired
    private DayLogMapper dayLogMapper;
    @Autowired
    private IHolidayService holidayService;
    @Autowired
    private IAskLeaveService askLeaveService;
    @Autowired
    private IWorkOvertimeService overtimeService;
    @Autowired
    private IPersonnelArchivesService personnelArchivesService;
    @Autowired
    private IAskLeaveSlaveService askLeaveSlaveService;
    @Autowired
    private IKqCalendarService kqCalendarService;
    @Autowired
    private IMonthLogMainService monthLogMainService;
    @Autowired
    private IWorkOvertimeSlaveService workOvertimeSlaveService;
    @Autowired
    private IDayLogGroupService dayLogGroupService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IDayLogContentService dayLogContentService;
    @Autowired
    private ISysDeptService sysDeptService;
    /**
     * 查询日志填报
     *
     * @param id 日志填报主键
     * @return 日志填报
     */
    @Override
    public DayLog selectDayLogById(Long id)
    {
        return dayLogMapper.selectDayLogById(id);
    }

    /**
     * 查询日志填报列表
     *
     * @param dayLog 日志填报
     * @return 日志填报
     */
    @Override
    public List<DayLog> selectDayLogList(DayLog dayLog)
    {
        return dayLogMapper.selectDayLogList(dayLog);
    }

    /**
     * 新增日志填报
     *
     * @param dayLog 日志填报
     * @return 结果
     */
    @Override
    public int insertDayLog(DayLog dayLog)
    {
        dayLog.setCreateTime(DateUtils.getNowDate());
        return dayLogMapper.insertDayLog(dayLog);
    }

    /**
     * 修改日志填报
     *
     * @param dayLog 日志填报
     * @return 结果
     */
    @Override
    public int updateDayLog(DayLog dayLog)
    {
        dayLog.setUpdateTime(DateUtils.getNowDate());
        return dayLogMapper.updateDayLog(dayLog);
    }

    /**
     * 批量删除日志填报
     *
     * @param ids 需要删除的日志填报主键
     * @return 结果
     */
    @Override
    public int deleteDayLogByIds(Long[] ids)
    {
        return dayLogMapper.deleteDayLogByIds(ids);
    }

    /**
     * 删除日志填报信息
     *
     * @param id 日志填报主键
     * @return 结果
     */
    @Override
    public int deleteDayLogById(Long id)
    {
        return dayLogMapper.deleteDayLogById(id);
    }

    @Override
    @Transactional
    public int saveDayLogList(List<DayLog> dayLogList){

        if (!dayLogList.isEmpty()){
            dayLogList.forEach(dayLog -> {
                dayLog.setLogStatus("1");
                if (dayLog.getId() ==null){
                    dayLog.setCreateBy(getLoginUser().getUsername());
                    dayLog.setCreateTime(DateUtils.getNowDate());
                    dayLogMapper.insertDayLog(dayLog);

                    if (dayLog.getLogContentList() != null && !dayLog.getLogContentList().isEmpty()){
                        for (DayLogContent logContent : dayLog.getLogContentList()) {
                            logContent.setMainId(dayLog.getId());
                        }
                        dayLogContentService.insertdayLogContentBatch(dayLog.getLogContentList());
                    }

                } else {
                    dayLog.setUpdateBy(getLoginUser().getUsername());
                    dayLog.setUpdateTime(DateUtils.getNowDate());
                    dayLogMapper.updateDayLog(dayLog);

                    dayLogContentService.deleteDayLogContentByMainId(dayLog.getId());
                    if (dayLog.getLogContentList() != null && !dayLog.getLogContentList().isEmpty()){
                        for (DayLogContent logContent : dayLog.getLogContentList()) {
                            logContent.setMainId(dayLog.getId());
                        }
                        dayLogContentService.insertdayLogContentBatch(dayLog.getLogContentList());
                    }
                }
            });
        }
        return 1;
    }

    @Override
    @Transactional
    public int commintDayLogList(List<DayLog> dayLogList){

        if (!dayLogList.isEmpty()){
            dayLogList.forEach(dayLog -> {
                dayLog.setLogStatus("2");
                if (dayLog.getId() ==null){
                    dayLog.setCreateBy(getLoginUser().getUsername());
                    dayLog.setCreateTime(DateUtils.getNowDate());
                    dayLogMapper.insertDayLog(dayLog);

                    if (dayLog.getLogContentList() != null && !dayLog.getLogContentList().isEmpty()){
                        for (DayLogContent logContent : dayLog.getLogContentList()) {
                            logContent.setMainId(dayLog.getId());
                        }
                        dayLogContentService.insertdayLogContentBatch(dayLog.getLogContentList());
                    }
                } else {
                    dayLog.setUpdateBy(getLoginUser().getUsername());
                    dayLog.setUpdateTime(DateUtils.getNowDate());
                    dayLogMapper.updateDayLog(dayLog);

                    dayLogContentService.deleteDayLogContentByMainId(dayLog.getId());
                    if (dayLog.getLogContentList() != null && !dayLog.getLogContentList().isEmpty()){
                        for (DayLogContent logContent : dayLog.getLogContentList()) {
                            logContent.setMainId(dayLog.getId());
                        }
                        dayLogContentService.insertdayLogContentBatch(dayLog.getLogContentList());
                    }
                }

            });
        }
        return 1;
    }

    @Override
    public LogVo getDayLogList(DayLogListVo dayLog) {
        LogVo logVo = new LogVo();
        //上报天数
        logVo.setReportingNum(0);
        //应报天数
        logVo.setShouldNum(0);


        dayLog.setMonthStart(dayLog.getMonthCondition() + "-01");
        dayLog.setMonthEnd(dayLog.getMonthCondition() + "-31");
        //获取本年的法定节假日
        //List<Holiday> holidayList = holidayService.getHolidayList();
        //查询本月的日期
        //List<DayLogListVo> dayLogList = dayLogMapper.getDayLogList(dayLog);
        List<DayLogListVo> dayLogList = kqCalendarService.selectKqCalendarListForMonth(dayLog);
        if (dayLogList.isEmpty()) {
            KqCalendar kqCalendar = new KqCalendar();
            kqCalendar.setYear(dayLog.getMonthCondition().substring(0, 4));
            Long count = kqCalendarService.selectKqCalendarCount(kqCalendar);
            if (count == 0) {
                kqCalendarService.selectKqCalendarList(kqCalendar);
            }
            dayLogList = kqCalendarService.selectKqCalendarListForMonth(dayLog);
        }

        // 创建日期到请假时间的映射（全天/半天）
        Map<Date, Double> leaveDateMap = new HashMap<>();
        // 创建日期到加班时间的映射（全天/半天）
        Map<Date, Double> overtimeDateMap = new HashMap<>();

        //查询本月请假
        AskLeaveSlaveVo leave = new AskLeaveSlaveVo();
        leave.setMonth(dayLog.getMonthCondition());
        leave.setState("3");
        leave.setEffective("0");
        leave.setCreateBy(getLoginUser().getUsername());
        List<AskLeaveSlave> askLeaves = askLeaveSlaveService.selectAskLeaveSlaveList(leave);
        logVo.setLeaveNum(0);

        if (!askLeaves.isEmpty()){
            // 计算请假总天数
            double totalLeaveTime = askLeaves.stream().mapToDouble(AskLeaveSlave::getTimes).sum();
            logVo.setLeaveNum(totalLeaveTime);

            // 处理请假数据，填充leaveDateMap
            askLeaves.forEach(item -> {
                LocalDate now = LocalDate.parse(dayLog.getMonthStart());
                LocalDate startDate = item.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate endDate = item.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

                // 跨月份请假
                if(startDate.getMonth() != endDate.getMonth()){
                    if (startDate.getMonth() == now.getMonth()){
                        // 获取该日期所在月份的最后一天
                        LocalDate lastDayOfMonth = startDate.withDayOfMonth(startDate.lengthOfMonth());

                        // 处理本月内的每一天
                        for (LocalDate date = startDate; !date.isAfter(lastDayOfMonth); date = date.plusDays(1)) {
                            Date currentDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());

                            // 如果是请假的第一天且不是全天
                            if (date.equals(startDate)) {
                                try {
                                    // 开始时间小于8小时按半天算
                                    long differenceInMillis = (createDate("17:30").getTime() - item.getStartTimePeriod().getTime()) / (1000 * 60 * 60);
                                    if (differenceInMillis < 8) {
                                        leaveDateMap.put(currentDate, 0.5); // 半天请假
                                    } else {
                                        leaveDateMap.put(currentDate, 1.0); // 全天请假
                                    }
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            } else {
                                leaveDateMap.put(currentDate, 1.0); // 全天请假
                            }
                        }
                    }
                    else if (endDate.getMonth() == now.getMonth()){
                        // 处理本月内的每一天
                        for (LocalDate date = LocalDate.of(now.getYear(), now.getMonth(), 1); !date.isAfter(endDate); date = date.plusDays(1)) {
                            Date currentDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());

                            // 如果是请假的最后一天
                            if (date.equals(endDate)) {
                                try {
                                    // 结束时间大于0小时按半天算
                                    long differenceInMillis = (createDate("17:30").getTime() - item.getEndTimePeriod().getTime()) / (1000 * 60 * 60);
                                    if (differenceInMillis > 0) {
                                        leaveDateMap.put(currentDate, 0.5); // 半天请假
                                    } else {
                                        leaveDateMap.put(currentDate, 1.0); // 全天请假
                                    }
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            } else {
                                leaveDateMap.put(currentDate, 1.0); // 全天请假
                            }
                        }
                    }
                } else {
                    // 非跨月请假
                    // 处理日期范围内的每一天
                    for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                        Date currentDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());

                        // 如果只请假一天且时间小于1天
                        if (startDate.equals(endDate) && item.getTimes() < 1.0) {
                            leaveDateMap.put(currentDate, item.getTimes()); // 半天请假
                        }
                        // 如果是请假的第一天且跨多天
                        else if (date.equals(startDate) && !startDate.equals(endDate)) {
                            try {
                                // 开始时间小于8小时按半天算
                                long differenceInMillis = (createDate("17:30").getTime() - item.getStartTimePeriod().getTime()) / (1000 * 60 * 60);
                                if (differenceInMillis < 8) {
                                    leaveDateMap.put(currentDate, 0.5); // 半天请假
                                } else {
                                    leaveDateMap.put(currentDate, 1.0); // 全天请假
                                }
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                        // 如果是请假的最后一天且跨多天
                        else if (date.equals(endDate) && !startDate.equals(endDate)) {
                            try {
                                // 结束时间大于0小时按半天算
                                long differenceInMillis = (createDate("17:30").getTime() - item.getEndTimePeriod().getTime()) / (1000 * 60 * 60);
                                if (differenceInMillis > 0) {
                                    leaveDateMap.put(currentDate, 0.5); // 半天请假
                                } else {
                                    leaveDateMap.put(currentDate, 1.0); // 全天请假
                                }
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                        // 中间的天数都是全天请假
                        else {
                            leaveDateMap.put(currentDate, 1.0); // 全天请假
                        }
                    }
                }
            });
        }

        //查询本月加班
        WorkOvertimeSlave overtime = new WorkOvertimeSlave();
        overtime.setMonth(dayLog.getMonthCondition());
        overtime.setState("3");
        overtime.setEffective("0");
        overtime.setCreateBy(getLoginUser().getUsername());
        List<WorkOvertimeSlave> overtimes = workOvertimeSlaveService.selectWorkOvertimeSlaveList(overtime);
        logVo.setOvertimeNum(0);

        if (!overtimes.isEmpty()){
            // 计算加班总天数
            double totalOvertimeTime = overtimes.stream().mapToDouble(WorkOvertimeSlave::getTimes).sum();
            logVo.setOvertimeNum(totalOvertimeTime);

            // 处理加班数据，填充overtimeDateMap
            overtimes.forEach(item -> {
                LocalDate now = LocalDate.parse(dayLog.getMonthStart());
                LocalDate startDate = item.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate endDate = item.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

                // 跨月份加班
                if(startDate.getMonth() != endDate.getMonth()){
                    if (startDate.getMonth() == now.getMonth()){
                        // 获取该日期所在月份的最后一天
                        LocalDate lastDayOfMonth = startDate.withDayOfMonth(startDate.lengthOfMonth());

                        // 处理本月内的每一天
                        for (LocalDate date = startDate; !date.isAfter(lastDayOfMonth); date = date.plusDays(1)) {
                            Date currentDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());

                            // 如果是加班的第一天
                            if (date.equals(startDate)) {
                                try {
                                    // 开始时间小于8小时按半天算
                                    long differenceInMillis = (createDate("17:30").getTime() - item.getStartTimePeriod().getTime()) / (1000 * 60 * 60);
                                    if (differenceInMillis < 8) {
                                        overtimeDateMap.put(currentDate, 0.5); // 半天加班
                                    } else {
                                        overtimeDateMap.put(currentDate, 1.0); // 全天加班
                                    }
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            } else {
                                overtimeDateMap.put(currentDate, 1.0); // 全天加班
                            }
                        }
                    }
                    else if (endDate.getMonth() == now.getMonth()){
                        // 处理本月内的每一天
                        for (LocalDate date = LocalDate.of(now.getYear(), now.getMonth(), 1); !date.isAfter(endDate); date = date.plusDays(1)) {
                            Date currentDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());

                            // 如果是加班的最后一天
                            if (date.equals(endDate)) {
                                try {
                                    // 结束时间大于0小时按半天算
                                    long differenceInMillis = (createDate("17:30").getTime() - item.getEndTimePeriod().getTime()) / (1000 * 60 * 60);
                                    if (differenceInMillis > 0) {
                                        overtimeDateMap.put(currentDate, 0.5); // 半天加班
                                    } else {
                                        overtimeDateMap.put(currentDate, 1.0); // 全天加班
                                    }
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            } else {
                                overtimeDateMap.put(currentDate, 1.0); // 全天加班
                            }
                        }
                    }
                } else {
                    // 非跨月加班
                    // 处理日期范围内的每一天
                    for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                        Date currentDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());

                        // 如果只加班一天且时间小于1天
                        if (startDate.equals(endDate) && item.getTimes() < 1.0) {
                            overtimeDateMap.put(currentDate, item.getTimes()); // 半天加班
                        }
                        // 如果是加班的第一天且跨多天
                        else if (date.equals(startDate) && !startDate.equals(endDate)) {
                            try {
                                // 开始时间小于8小时按半天算
                                long differenceInMillis = (createDate("17:30").getTime() - item.getStartTimePeriod().getTime()) / (1000 * 60 * 60);
                                if (differenceInMillis < 8) {
                                    overtimeDateMap.put(currentDate, 0.5); // 半天加班
                                } else {
                                    overtimeDateMap.put(currentDate, 1.0); // 全天加班
                                }
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                        // 如果是加班的最后一天且跨多天
                        else if (date.equals(endDate) && !startDate.equals(endDate)) {
                            try {
                                // 结束时间大于0小时按半天算
                                long differenceInMillis = (createDate("17:30").getTime() - item.getEndTimePeriod().getTime()) / (1000 * 60 * 60);
                                if (differenceInMillis > 0) {
                                    overtimeDateMap.put(currentDate, 0.5); // 半天加班
                                } else {
                                    overtimeDateMap.put(currentDate, 1.0); // 全天加班
                                }
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                        // 中间的天数都是全天加班
                        else {
                            overtimeDateMap.put(currentDate, 1.0); // 全天加班
                        }
                    }
                }
            });
        }

        // 获取请假和加班的日期列表（用于设置holiday状态）
        List<Date> askLeaveDates = new ArrayList<>(leaveDateMap.keySet());
        List<Date> overtimeDates = new ArrayList<>(overtimeDateMap.keySet());



        // 获取登录人日志
        dayLog.setCreateBy(getLoginUser().getUsername());
        List<DayLog> dayLogs = dayLogMapper.selectDayLogVoList(dayLog);
        Map<String, List<DayLog>> dayLogMap = dayLogs.stream().collect(Collectors.groupingBy(d -> d.getLogDate().toString()));

        // 计算应报天数和上报天数
        dayLogList.forEach(item -> {
            item.setUserId(getLoginUser().getUser().getUserId());
            item.setState(item.getHoliday());

            //关联每日的日志
            item.setDayLogList(dayLogMap.get(item.getDate().toString()));
            item.setReportBy(getLoginUser().getUser().getNickName());

            // 如果是请假，设置State为"1"
            if (askLeaveDates.contains(item.getDate())) {
                item.setHoliday("4");
                if (leaveDateMap.get(item.getDate()) != null && leaveDateMap.get(item.getDate()) >= 1.0) {
                    item.setState("1");
                }
            }
            if (overtimeDates.contains(item.getDate())) {
                item.setHoliday("3");
                item.setState("2");
            }
            // 获取当天的请假和加班情况
            Double leaveTime = leaveDateMap.get(item.getDate());
            Double overtimeTime = overtimeDateMap.get(item.getDate());

            // 计算应报天数
            // 1. 工作日需要填写日志
            if ("2".equals(item.getHoliday())) {
                logVo.setShouldNum(logVo.getShouldNum() + 1);
            }

            // 2. 半天请假需要填写日志，全天请假不需要
            if (leaveTime != null && leaveTime < 1.0) {
                logVo.setShouldNum(logVo.getShouldNum() + 1);
            }

            // 3. 加班日需要填写日志
            if (overtimeTime != null) {
                logVo.setShouldNum(logVo.getShouldNum() + 1);
            }

            // 修改日报状态
            if (item.getDayLogList() != null && !item.getDayLogList().isEmpty()) {
                DayLog log = item.getDayLogList().get(0);
                // 如果当天有日报，则为日志状态已填报并提交，修改日期状态为已填报，否则保持原样
                item.setState("2".equals(log.getLogStatus()) ? "3" : item.getState());
                item.setReportTime(log.getUpdateTime() != null ? log.getUpdateTime() : log.getCreateTime());

                // 计算上报天数（节假日填报不算入上报天数，全天请假不算入上报天数）
                if (!"1".equals(item.getHoliday()) && (leaveTime == null || leaveTime < 1.0)) {
                    logVo.setReportingNum("2".equals(log.getLogStatus()) ? logVo.getReportingNum() + 1 : logVo.getReportingNum());
                }
            }
        });

        // 状态筛选条件
        if (!"".equals(dayLog.getState()) && dayLog.getState() != null) {
            dayLogList = dayLogList.stream()
                    .filter(log -> dayLog.getState().equals(log.getState()))
                    .collect(Collectors.toList());
        }

        logVo.setRows(dayLogList);
        // 缺报天数
        logVo.setMissingNum(logVo.getShouldNum() - logVo.getReportingNum());
        return logVo;
    }

    /**
     * 查询个人日志列表
     * @param dayLog
     * @return
     */
    @Override
    public List<DayLogListVo> getDayLogListOther(DayLogListVo dayLog) {
        dayLog.setLogStatus("2");
        List<DayLog> dayLogs = dayLogMapper.selectDayLogVoList(dayLog);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if(dayLog.getLogContent() != null && !dayLog.getLogContent().isEmpty()){
            // 使用 Stream 流将 logDate 转换为 String 并收集到新的 List 中
            List<String> logDateStrings = dayLogs.stream()
                    .map(DayLog::getLogDate)
                    .filter(Objects::nonNull)
                    .map(date -> {
                        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                        return localDate.format(formatter);
                    }).collect(Collectors.toList());
            if (logDateStrings.isEmpty()){
                return new ArrayList<>();
            }
            dayLog.setLogDateList(logDateStrings);
        }


        if(dayLog.getLogDateStart() != null){
            LocalDate localDateStart = dayLog.getLogDateStart().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate localDateEnd = dayLog.getLogDateEnd().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            dayLog.setMonthStart(localDateStart.format(formatter));
            dayLog.setMonthEnd(localDateEnd.format(formatter));
        }

        //查询本月加班
        WorkOvertimeSlave overtime = new WorkOvertimeSlave();
        overtime.setMonth(dayLog.getMonthCondition());
        overtime.setState("3");
        overtime.setEffective("0");
        overtime.setCreateBy(getLoginUser().getUsername());
        List<WorkOvertimeSlave> overtimes = workOvertimeSlaveService.selectWorkOvertimeSlaveList(overtime);

        Map<Date, String> overtimeMap = parseOverTimeDateMap(overtimes);
        if (dayLog.getHoliday() != null && "2".equals(dayLog.getHoliday())){
            List<Date> overtimeDateList = new ArrayList<>(overtimeMap.keySet());
            dayLog.setOvertimeList(overtimeDateList);
        }
        PageUtil.startPage();
        List<DayLogListVo> dayLogList = kqCalendarService.selectKqCalendarListForMonthDesc(dayLog);

        if (dayLogList.isEmpty() && dayLog.getLogDateStart() != null) {
            return new ArrayList<>();
        }

        if (dayLogList.isEmpty()) {
            KqCalendar kqCalendar = new KqCalendar();
            if (dayLog.getLogDateStart() != null){
                // 假设 dayLog.getLogDateStart() 返回一个 Date 对象
                Date logDateStart = dayLog.getLogDateStart();
                // 创建 Calendar 对象并设置日期
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(logDateStart);
                // 获取年份并转换为字符串
                int year = calendar.get(Calendar.YEAR);
                if (year < 2024){
                    throw new ServiceException("查询日志所属日期年份不能小于2024年");
                }
                String yearString = String.valueOf(year);
                kqCalendar.setYear(yearString);
            }
            Long count = kqCalendarService.selectKqCalendarCount(kqCalendar);
            if (count == 0) {
                kqCalendarService.selectKqCalendarList(kqCalendar);
            }
            dayLogList = kqCalendarService.selectKqCalendarListForMonth(dayLog);
        }

        //获取登陆人日志
        Map<String, List<DayLog>> DayLogMap = dayLogs.stream().collect(Collectors.groupingBy(d -> d.getLogDate().toString()));

        dayLogList.forEach( item -> {
            item.setState(item.getHoliday());

            //关联每日的日志
            item.setDayLogList(DayLogMap.get(item.getDate().toString()));
            item.setReportBy(getLoginUser().getUser().getNickName());
            if (item.getDayLogList() != null && !item.getDayLogList().isEmpty()) {
                // 获取列表中第一个元素的DataFormat并设置到当前item
                DayLog firstItem = item.getDayLogList().get(0);
                item.setDataFormat(firstItem.getDataFormat());
            }
            //修改日报状态
            if (item.getDayLogList() != null && !item.getDayLogList().isEmpty()) {
                DayLog log = item.getDayLogList().get(0);
                //如果当天有日报，则为日志状态已填报并提交  修改日期状态为已填报 否则保持原样
                item.setState("2".equals(log.getLogStatus()) ? "3" : item.getState());
                item.setReportTime(log.getUpdateTime() != null? log.getUpdateTime() : log.getCreateTime());
            }
        });

        //查询本月请假
        AskLeaveSlaveVo leave = new AskLeaveSlaveVo();
        leave.setMonth(dayLog.getMonthCondition());
        leave.setState("3");
        leave.setEffective("0");
        leave.setCreateBy(getLoginUser().getUsername());
        List<AskLeaveSlave> askLeaves = askLeaveSlaveService.selectAskLeaveSlaveList(leave);



        //加班和请假计算
//        List<Date> askLeaveDates = parseLeaveDatesAskLeave(askLeaves);
//        List<Date> overtimeDates = parseLeaveDatesWorkOvertimes(overtimes);

        Map<Date, String> askLeaveMap = parseLeaveDateMap(askLeaves);


        dayLogList.forEach(log -> {
            if (askLeaveMap.containsKey(log.getDate())) {
                log.setHoliday("4");
                log.setProcessId(askLeaveMap.get(log.getDate()));
            }
            if (overtimeMap.containsKey(log.getDate())) {
                log.setHoliday("3");
                log.setProcessId(overtimeMap.get(log.getDate()));
            }
        });
        return dayLogList;
    }

    public List<DayLogExport> getDayLogListOtherExport(DayLogListVo dayLog){
        List<DayLogExport> export= new ArrayList<>();
        List<DayLogListVo> dayLogListOther = getDayLogListOther(dayLog);
        dayLogListOther.forEach(item -> {
            DayLogExport dayLogExport = new DayLogExport();
            dayLogExport.setDate(item.getDate());
            dayLogExport.setHoliday(item.getHoliday());
            dayLogExport.setLogStatus(item.getState());
            dayLogExport.setReportTime(item.getReportTime());
            String logContent = Optional.ofNullable(item.getDayLogList()) // 处理 getDayLogList() 为 null 的情况
                    .orElseGet(Collections::emptyList)                         // 若为 null，替换为空列表
                    .stream()
                    .map(log -> {
                        String dataFormat = log.getDataFormat();               // 假设 dataFormat 是 log 的字段
                        if ("1".equals(dataFormat)) {
                            return formatTime(log.getStartTime()) + " - " + formatTime(log.getEndTime()) + "\n"+
                                    Optional.ofNullable(log.getLogContent())    // 处理 logContent 为 null
                                    .orElse("");                               // 空内容用空字符串代替
                        } else if ("2".equals(dataFormat)) {
                            return formatTime(log.getStartTime()) + " - " + formatTime(log.getEndTime()) + "\n"+
                                    Optional.ofNullable(log.getLogContentList())
                                            .orElseGet(Collections::emptyList)
                                            .stream()
                                            .map(DayLogContent::getLogContent)
                                            .filter(Objects::nonNull)
                                            .filter(vo -> !vo.isEmpty())
                                            // 通过 Collectors.collectingAndThen 分两步处理：先收集列表，再添加序号
                                            .collect(Collectors.collectingAndThen(
                                                    Collectors.toList(),
                                                    list -> IntStream.range(0, list.size())
                                                            .mapToObj(i -> (i + 1) + ". " + list.get(i)) // 添加序号
                                                            .collect(Collectors.joining("\n"))
                                            ));
                        }
                        return null; // 或返回默认值
                    })
                    .filter(Objects::nonNull)                                  // 过滤掉无效的 dataFormat 分支
                    .collect(Collectors.joining("\n"));                        // 合并所有内容
            dayLogExport.setLogContent(logContent);
            export.add(dayLogExport);
        });
        return export;
    }

    public List<DayLogListVo> getDayLogListOfLeader(DayLogListVo dayLog){
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = personnelArchivesService.getDataRangeFilterRoleKey(loginUser,"rz-");
        dayLog.setDeptIds(dataRange.get("deptIds"));
        dayLog.setUnitIds(dataRange.get("unitIds"));
        dayLog.setCreateBy(loginUser.getUser().getUserName());
        //List<String> createByList = personnelArchivesService.getSubordinateList().stream().map(PersonnelArchivesVo::getSysName).collect(Collectors.toList());

        Long userId = getLoginUser().getUser().getUserId();
        List<PersonnelArchivesVo> archivesVoList = personnelArchivesService.selectListOfMonthLogUserId(userId);
        //查询登陆人 直属下属人员档案  以及下属的下属
//        List<PersonnelArchivesVo> collect = archivesVoList.stream()
//                .filter(vo -> vo != null && vo.getDirectSuperior() != null && vo.getDirectSuperior().equals(userId))
//                .flatMap(vo -> Stream.concat(Stream.of(vo), findSubordinates(archivesVoList, vo.getId()).stream()))  //获取下属的下属
//                .collect(Collectors.toList());
        //根据人员档案list,获取人员的登陆名 用来查询月报
        List<String> createByList = archivesVoList.stream().map(PersonnelArchivesVo::getSysName).collect(Collectors.toList());
        dayLog.setCreateByList(createByList);
        if(dayLog.getGroupId() != null && (dayLog.getGroupUserIdList() == null || dayLog.getGroupUserIdList().isEmpty())){
            DayLogGroup dayLogGroup = dayLogGroupService.selectDayLogGroupById(dayLog.getGroupId());
            dayLog.setGroupUserIdList(dayLogGroup.getUserIdList());
        }
        PageUtil.startPage();
        List<DayLogListVo> dayLogListOfLeader = dayLogMapper.getDayLogListOfLeader(dayLog);
        List<String> cbList = dayLogListOfLeader.stream().map(DayLogListVo::getCreateBy).collect(Collectors.toList());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> ldList = dayLogListOfLeader.stream().map(DayLogListVo::getLogDate).map(sdf::format).collect(Collectors.toList());
        DayLogListVo dayLogListVo = new DayLogListVo();
        dayLogListVo.setCreateByList(cbList);
        dayLogListVo.setLogDateList(ldList);
        List<DayLogListVo> dayLogListVos = dayLogMapper.selectDayLogOfList(dayLogListVo);

        return dayLogListOfLeader;
    }

    // 创建 Date 对象，假设日期为同一天
    public Date createDate(String timeStr) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        return sdf.parse(timeStr);
    }

    @Override
    public List<SysUser> selectUserList(SysUser user)
    {
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = personnelArchivesService.getDataRangeFilterRoleKey(loginUser,"rz-");
        user.setDeptIds(dataRange.get("deptIds"));
        user.setUnitIds(dataRange.get("unitIds"));
        //List<String> createByList = personnelArchivesService.getSubordinateList().stream().map(PersonnelArchivesVo::getSysName).collect(Collectors.toList());

        List<PersonnelArchivesVo> archivesVoList = personnelArchivesService.selectListOfMonthLog();
        Long userId = getLoginUser().getUser().getUserId();
        //查询登陆人 直属下属人员档案  以及下属的下属
        List<PersonnelArchivesVo> collect = archivesVoList.stream()
                .filter(vo -> vo != null && vo.getDirectSuperior() != null && vo.getDirectSuperior().equals(userId))
                .flatMap(vo -> Stream.concat(Stream.of(vo), findSubordinates(archivesVoList, vo.getId()).stream()))  //获取下属的下属
                .collect(Collectors.toList());
        //根据人员档案list,获取人员的登陆名 用来查询月报
        List<String> createByList = collect.stream().map(PersonnelArchivesVo::getSysName).collect(Collectors.toList());
        //至少权限有自己数据
        createByList.add(loginUser.getUser().getUserName());
        if (user.getUserNames()!= null &&!user.getUserNames().isEmpty()){
            createByList = user.getUserNames();
        }
        user.setUserNames(createByList);
        PageUtil.startPage();
        List<SysUser> sysUsers = sysUserService.selectUserListOfDayLog(user);

        List<SysDept> deptList = sysDeptService.selectDeptList(new SysDept());
        sysUsers.forEach(item -> {
            List<SysUserPost> userPostList = item.getUserPostList();
            Long deptId = userPostList.stream()
                    .filter(post -> "0".equals(post.getHomePost()))  // 筛选 homepost=0 的对象
                    .map(SysUserPost::getDeptId)              // 提取 deptId
                    .findFirst()                              // 获取第一个匹配项
                    .orElse(null);// 若未找到，返回 null（可替换为默认值或抛出异常）
            item.setDeptChain(buildDeptChain(deptId,deptList));
        });

        return sysUsers;
    }
    public List<Date> parseLeaveDatesWorkOvertimes(List<WorkOvertimeSlave> workOvertimes) {
        Set<Date> dateSet = new LinkedHashSet<>(); // 去重且保序
        for (WorkOvertimeSlave leave : workOvertimes) {
            // 截断时间部分（保留日期）
            Date startDate = leave.getStartTime();
            Date endDate = leave.getEndTime();

            if (startDate == null || endDate == null || startDate.after(endDate)) {
                continue; // 跳过无效数据
            }

            // 遍历日期区间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            while (!calendar.getTime().after(endDate)) {
                dateSet.add(calendar.getTime());
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
        }
        return new ArrayList<>(dateSet);
    }

    public List<Date> parseLeaveDatesAskLeave(List<AskLeaveSlave> askLeaves) {
        Set<Date> dateSet = new LinkedHashSet<>(); // 去重且保序
        for (AskLeaveSlave leave : askLeaves) {
            // 截断时间部分（保留日期）
            Date startDate = leave.getStartTime();
            Date endDate = leave.getEndTime();

            if (startDate == null || endDate == null || startDate.after(endDate)) {
                continue; // 跳过无效数据
            }

            // 遍历日期区间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            while (!calendar.getTime().after(endDate)) {
                dateSet.add(calendar.getTime());
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
        }
        return new ArrayList<>(dateSet);
    }

    public static String formatTime(Date date) {
        if (date == null) return "";
        LocalTime localTime = date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalTime(); // 直接转为 LocalTime
        return localTime.format(DateTimeFormatter.ofPattern("HH:mm"));
    }

    public Map<Date, String> parseLeaveDateMap(List<AskLeaveSlave> askLeaves) {
        Map<Date, String> dateStringMap = new LinkedHashMap<>(); // 保序
        for (AskLeaveSlave leave : askLeaves) {
            // 截断时间部分（保留日期）
            Date startDate = leave.getStartTime();
            Date endDate = leave.getEndTime();

            if (startDate == null || endDate == null || startDate.after(endDate)) {
                continue; // 跳过无效数据
            }

            // 遍历日期区间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            while (!calendar.getTime().after(endDate)) {
                Date currentDate = calendar.getTime();
                dateStringMap.put(currentDate, leave.getProcessId());
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
        }
        return dateStringMap;
    }

    public Map<Date, String> parseOverTimeDateMap(List<WorkOvertimeSlave> workOvertimes) {
        Map<Date, String> dateStringMap = new LinkedHashMap<>(); // 保序
        for (WorkOvertimeSlave overtime : workOvertimes) {
            // 截断时间部分（保留日期）
            Date startDate = overtime.getStartTime();
            Date endDate = overtime.getEndTime();

            if (startDate == null || endDate == null || startDate.after(endDate)) {
                continue; // 跳过无效数据
            }

            // 遍历日期区间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            while (!calendar.getTime().after(endDate)) {
                Date currentDate = calendar.getTime();
                dateStringMap.put(currentDate, overtime.getProcessId());
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
        }
        return dateStringMap;
    }
}
