package org.ruoyi.core.kaoqin.mapper;

import org.ruoyi.core.kaoqin.domain.DayLog;
import org.ruoyi.core.kaoqin.domain.vo.DayLogListVo;

import java.util.List;

/**
 * 日志填报Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
public interface DayLogMapper
{
    /**
     * 查询日志填报
     *
     * @param id 日志填报主键
     * @return 日志填报
     */
    public DayLog selectDayLogById(Long id);

    /**
     * 查询日志填报列表
     *
     * @param dayLog 日志填报
     * @return 日志填报集合
     */
    public List<DayLog> selectDayLogList(DayLog dayLog);

    /**
     * 查询日志填报列表
     *
     * @param dayLog 日志填报
     * @return 日志填报集合
     */
    public List<DayLog> selectDayLogVoList(DayLogListVo dayLog);

    /**
     * 新增日志填报
     *
     * @param dayLog 日志填报
     * @return 结果
     */
    public int insertDayLog(DayLog dayLog);

    /**
     * 修改日志填报
     *
     * @param dayLog 日志填报
     * @return 结果
     */
    public int updateDayLog(DayLog dayLog);

    /**
     * 删除日志填报
     *
     * @param id 日志填报主键
     * @return 结果
     */
    public int deleteDayLogById(Long id);

    /**
     * 批量删除日志填报
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDayLogByIds(Long[] ids);

    public List<DayLogListVo> getDayLogList(DayLog dayLog);

    public int replaceDayLogBatch(List<DayLog> dayLogList);

    public int insertDayLogBatch(List<DayLog> dayLogList);

    public List<DayLogListVo> getDayLogListOfLeader(DayLog dayLog);

    /**
     * 优化版本的领导查询日志列表
     * @param dayLog 查询条件
     * @return 日志列表
     */
    public List<DayLogListVo> getDayLogListOfLeaderOptimized(DayLogListVo dayLog);
}
