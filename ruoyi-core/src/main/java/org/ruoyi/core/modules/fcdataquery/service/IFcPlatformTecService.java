package org.ruoyi.core.modules.fcdataquery.service;

import org.ruoyi.core.modules.fcdataquery.po.PlatformTecServiceDetailPo;
import org.ruoyi.core.modules.fcdataquery.po.PlatformTecServicePo;
import org.ruoyi.core.modules.fcdataquery.vo.PlatformTecServiceDetailVo;
import org.ruoyi.core.modules.fcdataquery.vo.PlatformTecServiceVo;

import java.util.List;

/**
 * 运营部-平台技术服务费Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IFcPlatformTecService
{

    /**
     * <AUTHOR>
     * @Description 运营部-平台技术服务费统计
     * @Date 2024/10/23 9:25
     * @Param [startTime, endTime]
     * @return void
     **/
    public void platformTecServiceSts(String dateStat);

    /**
     * <AUTHOR>
     * @Description 查询运营部-平台技术服务费列表
     * @Date 2024/10/14 14:11
     * @Param [platformTecServiceVo]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.PlatformTecServicePo>
     **/
    List<PlatformTecServicePo> selectPlatformTecServiceList(PlatformTecServiceVo platformTecServiceVo);

    /**
     * <AUTHOR>
     * @Description 获取平台技术服务费详情
     * @Date 2024/10/30 15:32
     * @Param [platformTecServiceDetailVo]
     * @return org.ruoyi.core.modules.fcdataquery.po.PlatformTecServiceDetailPo
     **/
    List<PlatformTecServiceDetailPo> getPlatformTecServiceDetail(PlatformTecServiceDetailVo platformTecServiceDetailVo);
}
