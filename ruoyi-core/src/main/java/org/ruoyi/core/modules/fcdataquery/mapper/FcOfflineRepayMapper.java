package org.ruoyi.core.modules.fcdataquery.mapper;

import org.ruoyi.core.modules.fcdataquery.po.OfflineRepayDetailPo;
import org.ruoyi.core.modules.fcdataquery.vo.OfflineRepayDetailVo;

import java.util.List;

/**
 * 运营部-线下还款Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface FcOfflineRepayMapper
{
    /**
     * <AUTHOR>
     * @Description 获取凭证明细
     * @Date 2024/11/20 14:42
     * @Param [offlineRepayDetailVo]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.OfflineRepayDetailPo>
     **/
    List<OfflineRepayDetailPo> getVoucherDetailByProject(OfflineRepayDetailVo offlineRepayDetailVo);
}
